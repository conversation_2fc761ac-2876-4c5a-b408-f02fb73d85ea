# Vercel Deployment Guide

This guide will help you deploy the Resilient Cape Admin Dashboard to Vercel.

## 🚀 Quick Deployment Steps

### 1. Prepare Your Repository

Make sure your code is pushed to GitHub:
```bash
git add .
git commit -m "Prepare for Vercel deployment"
git push origin main
```

### 2. Set Up MongoDB Atlas (Required for Vercel)

Since Vercel is serverless, you need a cloud database:

1. **Create MongoDB Atlas Account:**
   - Go to [MongoDB Atlas](https://www.mongodb.com/atlas)
   - Create a free account
   - Create a new cluster

2. **Get Connection String:**
   - Click "Connect" on your cluster
   - Choose "Connect your application"
   - Copy the connection string
   - Replace `<password>` with your database password

### 3. Deploy to Vercel

#### Option A: Vercel CLI
```bash
# Install Vercel CLI
npm install -g vercel

# Login to Vercel
vercel login

# Deploy
vercel

# Follow the prompts:
# - Link to existing project? No
# - Project name: resilient-cape-admin-dashboard
# - Directory: ./
```

#### Option B: Vercel Dashboard
1. Go to [vercel.com](https://vercel.com)
2. Sign in with GitHub
3. Click "New Project"
4. Import your repository
5. Configure as shown below

### 4. Configure Environment Variables

In Vercel dashboard, go to your project → Settings → Environment Variables:

| Variable | Value | Environment |
|----------|-------|-------------|
| `MONGODB_URI` | `mongodb+srv://username:<EMAIL>/resilient-cape-admin` | Production |
| `JWT_SECRET` | `your-super-secure-64-character-secret-key-for-production` | Production |
| `JWT_EXPIRES_IN` | `24h` | Production |
| `NODE_ENV` | `production` | Production |
| `CORS_ORIGIN` | `https://your-vercel-app.vercel.app` | Production |

### 5. Update Frontend API URLs

Create a production configuration in your client files:

```javascript
// In client/auth.js and client/app.js, update the baseURL:
const API_BASE_URL = process.env.NODE_ENV === 'production' 
  ? 'https://your-vercel-app.vercel.app/api'
  : 'http://localhost:5000/api';
```

Or create a config file:

```javascript
// client/config.js
const config = {
  API_BASE_URL: window.location.hostname === 'localhost' 
    ? 'http://localhost:5000/api'
    : 'https://your-vercel-app.vercel.app/api'
};
```

## 🔧 Vercel-Specific Configuration

### vercel.json Explanation

```json
{
  "version": 2,
  "builds": [
    {
      "src": "server/index.js",
      "use": "@vercel/node"
    },
    {
      "src": "client/**/*",
      "use": "@vercel/static"
    }
  ],
  "routes": [
    {
      "src": "/api/(.*)",
      "dest": "/server/index.js"
    },
    {
      "src": "/(.*)",
      "dest": "/client/$1"
    }
  ]
}
```

This configuration:
- Builds the Node.js server as a serverless function
- Serves client files as static assets
- Routes API calls to the server function
- Routes all other requests to static files

### Environment Variables for Vercel

```bash
# Set via Vercel CLI
vercel env add MONGODB_URI
vercel env add JWT_SECRET
vercel env add CORS_ORIGIN

# Or set via Vercel Dashboard
# Project → Settings → Environment Variables
```

## 🔄 Automatic Deployments

### GitHub Integration

1. **Connect GitHub:**
   - In Vercel dashboard, go to your project
   - Settings → Git → Connect GitHub repository

2. **Automatic Deployments:**
   - Every push to `main` branch triggers deployment
   - Pull requests create preview deployments
   - Environment variables are automatically applied

### Custom Domains

1. **Add Custom Domain:**
   - Project → Settings → Domains
   - Add your domain name
   - Configure DNS records as shown

2. **SSL Certificate:**
   - Automatically provided by Vercel
   - No additional configuration needed

## 🐛 Troubleshooting Vercel Deployment

### Common Issues

1. **CORS Errors:**
   ```javascript
   // Update CORS_ORIGIN environment variable to your Vercel URL
   CORS_ORIGIN=https://your-app.vercel.app
   ```

2. **Database Connection Issues:**
   ```javascript
   // Ensure MongoDB Atlas allows connections from anywhere (0.0.0.0/0)
   // Or add Vercel's IP ranges to whitelist
   ```

3. **Function Timeout:**
   ```json
   // In vercel.json, increase timeout
   "functions": {
     "server/index.js": {
       "maxDuration": 30
     }
   }
   ```

4. **Static File Issues:**
   ```json
   // Ensure client files are properly routed
   "routes": [
     { "src": "/api/(.*)", "dest": "/server/index.js" },
     { "src": "/(.*)", "dest": "/client/$1" }
   ]
   ```

### Debug Deployment

```bash
# Check deployment logs
vercel logs

# Check function logs
vercel logs --follow

# Test specific endpoints
curl https://your-app.vercel.app/api/health
```

## 📱 Frontend Configuration for Production

Update your client-side code to work with Vercel:

### 1. Update API Base URL

```javascript
// client/config.js
const config = {
  API_BASE_URL: window.location.hostname.includes('vercel.app') || window.location.hostname.includes('your-domain.com')
    ? `${window.location.origin}/api`
    : 'http://localhost:5000/api'
};

// Export for use in other files
window.CONFIG = config;
```

### 2. Update auth.js and app.js

```javascript
// Replace hardcoded API_BASE_URL with:
const API_BASE_URL = window.CONFIG ? window.CONFIG.API_BASE_URL : 'http://localhost:5000/api';
```

### 3. Include config in HTML files

```html
<!-- Add to all HTML files before other scripts -->
<script src="config.js"></script>
<script src="auth.js"></script>
<script src="app.js"></script>
```

## 🔐 Production Security for Vercel

### Environment Variables Security

1. **Never commit sensitive data:**
   ```bash
   # Add to .gitignore
   .env
   .env.local
   .env.production
   admin-credentials.json
   ```

2. **Use Vercel Environment Variables:**
   - All sensitive data should be in Vercel environment variables
   - Never hardcode secrets in your code

3. **JWT Secret Generation:**
   ```bash
   # Generate a strong JWT secret
   node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
   ```

### Database Security

1. **MongoDB Atlas Security:**
   - Enable authentication
   - Use strong passwords
   - Whitelist IP addresses (or 0.0.0.0/0 for Vercel)
   - Enable encryption at rest

2. **Connection Security:**
   - Use connection string with authentication
   - Enable SSL/TLS connections

## 📋 Deployment Checklist

### Pre-Deployment
- [ ] Code pushed to GitHub
- [ ] MongoDB Atlas cluster created
- [ ] Environment variables prepared
- [ ] CORS origins updated
- [ ] API URLs configured for production

### Vercel Configuration
- [ ] vercel.json file created
- [ ] Environment variables set in Vercel dashboard
- [ ] Custom domain configured (optional)
- [ ] SSL certificate verified

### Post-Deployment Testing
- [ ] Health endpoint accessible
- [ ] Registration works
- [ ] Login works
- [ ] Protected routes work with Bearer token
- [ ] File uploads work
- [ ] All CRUD operations work

## 🌐 Example Vercel URLs

After deployment, your URLs will be:
- **Frontend:** `https://your-app.vercel.app`
- **API:** `https://your-app.vercel.app/api`
- **Login:** `https://your-app.vercel.app/login.html`
- **Register:** `https://your-app.vercel.app/register.html`

## 🔄 Continuous Deployment

### Automatic Updates
- Push to `main` branch → Automatic deployment
- Pull requests → Preview deployments
- Environment variables → Automatically applied

### Manual Deployment
```bash
# Deploy specific branch
vercel --prod

# Deploy with custom domain
vercel --prod --scope your-team
```

---

**🎉 Your admin dashboard will be live on Vercel with full authentication and Bearer token support!**
