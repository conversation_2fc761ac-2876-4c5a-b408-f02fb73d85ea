<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Test - Resilient Cape Admin Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="auth.css">
</head>
<body>
    <div class="container">
        <!-- Navigation Bar -->
        <nav class="dashboard-nav">
            <div class="nav-brand">Resilient Cape Admin</div>
            <div class="nav-links">
                <a href="index.html" class="nav-link active">Dashboard</a>
                <a href="users.html" class="nav-link">User Management</a>
            </div>
            <div class="nav-user">
                <div class="user-info">
                    <span class="user-name">admin</span>
                    <span class="user-role super_admin">Super Admin</span>
                </div>
                <button id="logoutBtn" class="logout-btn">Logout</button>
            </div>
        </nav>
        
        <header>
            <h1>Navigation Test Page</h1>
        </header>

        <main>
            <section>
                <h2>Navigation Bar Test</h2>
                <p>This page shows how the navigation bar should look with proper styling.</p>
                
                <div style="background: #f8f9fa; padding: 2rem; border-radius: 10px; margin: 2rem 0;">
                    <h3>Navigation Features:</h3>
                    <ul>
                        <li>✅ Beautiful gradient brand logo with shield icon</li>
                        <li>✅ Interactive navigation links with hover effects</li>
                        <li>✅ Professional user info section</li>
                        <li>✅ Role badge with crown icon for Super Admin</li>
                        <li>✅ Logout button with door icon</li>
                        <li>✅ Responsive design for all screen sizes</li>
                        <li>✅ Smooth animations and transitions</li>
                    </ul>
                </div>
                
                <div style="background: #e8f5e8; padding: 2rem; border-radius: 10px; margin: 2rem 0;">
                    <h3>🎨 Design Elements:</h3>
                    <ul>
                        <li><strong>Glass-morphism:</strong> Frosted glass effects with backdrop blur</li>
                        <li><strong>Gradients:</strong> Beautiful color transitions</li>
                        <li><strong>Shadows:</strong> Professional depth and elevation</li>
                        <li><strong>Icons:</strong> Emoji icons for visual appeal</li>
                        <li><strong>Typography:</strong> Clean, readable fonts</li>
                    </ul>
                </div>
                
                <div style="background: #fff3cd; padding: 2rem; border-radius: 10px; margin: 2rem 0;">
                    <h3>📱 Responsive Behavior:</h3>
                    <ul>
                        <li><strong>Desktop:</strong> Horizontal layout with all elements visible</li>
                        <li><strong>Tablet:</strong> Adjusted spacing and sizing</li>
                        <li><strong>Mobile:</strong> Vertical stack with centered elements</li>
                    </ul>
                </div>
            </section>
        </main>
    </div>

    <script>
        // Test logout button
        document.getElementById('logoutBtn').addEventListener('click', () => {
            alert('Logout clicked! In real app, this would redirect to login page.');
        });
        
        // Test navigation links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                alert(`Navigation to ${link.textContent} clicked!`);
            });
        });
    </script>
</body>
</html>
