#!/usr/bin/env node

/**
 * Simple Bearer Token Generator
 * Usage: node get-token-simple.js username password
 */

const [,, username, password] = process.argv;

if (!username || !password) {
    console.log('Usage: node get-token-simple.js <username> <password>');
    console.log('Example: node get-token-simple.js admin admin123');
    process.exit(1);
}

async function getToken() {
    const API_BASE_URL = 'http://localhost:5000/api';
    
    try {
        console.log('🔐 Getting Bearer token...');
        
        const response = await fetch(`${API_BASE_URL}/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        });

        const data = await response.json();

        if (response.ok) {
            console.log('✅ Login successful!');
            console.log('\n📋 Your Bearer Token:');
            console.log('─'.repeat(80));
            console.log(data.data.token);
            console.log('─'.repeat(80));
            
            console.log('\n💡 Test it with curl:');
            console.log(`curl -H "Authorization: Bearer ${data.data.token}" \\`);
            console.log('  http://localhost:5000/api/user-profiles');
            
            // Test the token immediately
            console.log('\n🧪 Testing token...');
            const testResponse = await fetch(`${API_BASE_URL}/user-profiles`, {
                headers: {
                    'Authorization': `Bearer ${data.data.token}`
                }
            });
            
            const testData = await testResponse.json();
            
            if (testResponse.ok) {
                console.log(`✅ Token works! Found ${testData.count} profiles`);
                
                if (testData.data.length > 0) {
                    console.log('\n📊 Sample profile data:');
                    const sample = testData.data[0];
                    console.log(`   Name: ${sample.name}`);
                    console.log(`   Job: ${sample.jobTitle}`);
                    console.log(`   ID: ${sample._id}`);
                }
            } else {
                console.log('❌ Token test failed:', testData.error);
            }
            
        } else {
            console.log('❌ Login failed:', data.error);
            console.log('\n💡 Make sure:');
            console.log('   • Username and password are correct');
            console.log('   • You have registered an account');
            console.log('   • Server is running (npm run server)');
        }

    } catch (error) {
        console.error('❌ Error:', error.message);
        console.log('\n💡 Make sure the server is running: npm run server');
    }
}

// Check if fetch is available
if (typeof fetch === 'undefined') {
    console.log('❌ This script requires Node.js 18+ or install node-fetch');
    console.log('💡 Install node-fetch: npm install node-fetch@2');
    process.exit(1);
}

getToken();
