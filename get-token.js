#!/usr/bin/env node

/**
 * Get Bearer Token Script
 * This script helps you get a valid JWT token for API testing
 */

const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function getToken() {
  console.log('\n🔑 Get Bearer Token for API Access\n');

  const baseURL = 'http://localhost:5000/api';

  try {
    // Check if server is running
    console.log('🔍 Checking server status...');
    const healthResponse = await fetch(`${baseURL}/health`);

    if (!healthResponse.ok) {
      console.log('❌ Server is not running. Please start it with: npm run server');
      return;
    }

    console.log('✅ Server is running\n');

    // Get login credentials
    console.log('Please provide your login credentials:');
    const username = await question('Username: ');
    const password = await question('Password: ');

    // Login and get token
    console.log('\n🔐 Logging in...');
    const loginResponse = await fetch(`${baseURL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ username, password })
    });

    const loginData = await loginResponse.json();

    if (!loginResponse.ok) {
      console.log('❌ Login failed:', loginData.error);
      console.log('\n💡 Make sure you have registered an account first!');
      console.log('   Visit: http://localhost:8080/register.html');
      return;
    }

    const token = loginData.data.token;

    console.log('\n🎉 Login successful!');
    console.log('\n📋 Your Bearer Token:');
    console.log('─'.repeat(80));
    console.log(token);
    console.log('─'.repeat(80));

    console.log('\n💡 Usage Examples:');
    console.log('\n1. Using curl:');
    console.log(`curl -H "Authorization: Bearer ${token}" \\`);
    console.log('  http://localhost:5000/api/user-profiles');

    console.log('\n2. Using JavaScript:');
    console.log('fetch("http://localhost:5000/api/user-profiles", {');
    console.log('  headers: {');
    console.log(`    "Authorization": "Bearer ${token}"`);
    console.log('  }');
    console.log('})');

    console.log('\n3. Using Postman:');
    console.log('   Header: Authorization');
    console.log(`   Value: Bearer ${token}`);

    // Test the token
    console.log('\n🧪 Testing token with API call...');
    const testResponse = await fetch(`${baseURL}/user-profiles`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const testData = await testResponse.json();

    if (testResponse.ok) {
      console.log('✅ Token works! Found', testData.count, 'profiles');
    } else {
      console.log('❌ Token test failed:', testData.error);
    }

    console.log('\n⏰ Token expires in 24 hours');
    console.log('🔄 Run this script again to get a new token when needed');

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.log('\n💡 Make sure:');
    console.log('   • Server is running (npm run server)');
    console.log('   • MongoDB is connected');
    console.log('   • You have registered a user account');
  } finally {
    rl.close();
  }
}

// Check if fetch is available (Node.js 18+)
if (typeof fetch === 'undefined') {
  console.log('❌ This script requires Node.js 18+ or install node-fetch');
  console.log('💡 Install node-fetch: npm install node-fetch@2');
  process.exit(1);
}

getToken();