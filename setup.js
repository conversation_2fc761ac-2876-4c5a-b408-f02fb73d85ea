#!/usr/bin/env node

/**
 * Setup script for Resilient Cape Admin Dashboard
 * This script helps create the first admin user
 */

const readline = require('readline');
const fs = require('fs');
const path = require('path');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function setup() {
  console.log('\n🚀 Welcome to Resilient Cape Admin Dashboard Setup!\n');
  
  // Check if .env exists
  if (!fs.existsSync('.env')) {
    console.log('📝 Creating .env file from template...');
    fs.copyFileSync('.env.example', '.env');
    console.log('✅ .env file created successfully!\n');
  }

  console.log('🔧 Let\'s configure your admin dashboard:\n');

  // Get MongoDB URI
  const mongoUri = await question('MongoDB URI (press Enter for default): ');
  
  // Get JWT Secret
  const jwtSecret = await question('JWT Secret (press Enter to generate random): ');
  
  // Get admin credentials
  console.log('\n👤 Create your first admin user:');
  const username = await question('Admin username: ');
  const email = await question('Admin email: ');
  const password = await question('Admin password (min 6 chars): ');

  // Update .env file
  let envContent = fs.readFileSync('.env', 'utf8');
  
  if (mongoUri.trim()) {
    envContent = envContent.replace(
      /MONGODB_URI=.*/,
      `MONGODB_URI=${mongoUri.trim()}`
    );
  }
  
  if (jwtSecret.trim()) {
    envContent = envContent.replace(
      /JWT_SECRET=.*/,
      `JWT_SECRET=${jwtSecret.trim()}`
    );
  } else {
    // Generate random JWT secret
    const randomSecret = require('crypto').randomBytes(32).toString('hex');
    envContent = envContent.replace(
      /JWT_SECRET=.*/,
      `JWT_SECRET=${randomSecret}`
    );
  }

  fs.writeFileSync('.env', envContent);

  // Save admin credentials for later use
  const adminCredentials = {
    username: username.trim(),
    email: email.trim(),
    password: password.trim(),
    role: 'admin'
  };

  fs.writeFileSync('admin-credentials.json', JSON.stringify(adminCredentials, null, 2));

  console.log('\n✅ Configuration complete!');
  console.log('\n📋 Next steps:');
  console.log('1. Start the server: npm run server');
  console.log('2. Start the frontend: cd client && python3 -m http.server 8080');
  console.log('3. Visit http://localhost:8080/register.html');
  console.log('4. Register with the credentials you just provided');
  console.log('\n🔑 Your admin credentials have been saved to admin-credentials.json');
  console.log('⚠️  Remember to delete admin-credentials.json after setup for security!');
  
  rl.close();
}

setup().catch(console.error);
