#!/usr/bin/env node

/**
 * API Testing Script for Resilient Cape Admin Dashboard
 * This script demonstrates how to use the Bearer token authentication
 */

const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function testAPI() {
  console.log('\n🧪 API Testing Script for Resilient Cape Admin Dashboard\n');
  
  const baseURL = 'http://localhost:5000/api';
  
  try {
    // Check if server is running
    console.log('🔍 Checking if server is running...');
    const healthResponse = await fetch(`${baseURL}/health`);
    const healthData = await healthResponse.json();
    
    if (!healthResponse.ok) {
      console.log('❌ Server is not running. Please start it with: npm run server');
      return;
    }
    
    console.log('✅ Server is running:', healthData.message);
    
    // Get login credentials
    console.log('\n🔐 Please provide your login credentials:');
    const username = await question('Username: ');
    const password = await question('Password: ');
    
    // Test login
    console.log('\n🔑 Testing login...');
    const loginResponse = await fetch(`${baseURL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ username, password })
    });
    
    const loginData = await loginResponse.json();
    
    if (!loginResponse.ok) {
      console.log('❌ Login failed:', loginData.error);
      console.log('💡 Try registering first at: http://localhost:8080/register.html');
      return;
    }
    
    console.log('✅ Login successful!');
    console.log('🎫 Token received:', loginData.data.token.substring(0, 50) + '...');
    
    const token = loginData.data.token;
    
    // Test protected route
    console.log('\n📋 Testing protected route access...');
    const profilesResponse = await fetch(`${baseURL}/user-profiles`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    const profilesData = await profilesResponse.json();
    
    if (profilesResponse.ok) {
      console.log('✅ Protected route access successful!');
      console.log(`📊 Found ${profilesData.count} user profiles`);
      
      if (profilesData.data.length > 0) {
        console.log('\n👥 Sample profile:');
        const sample = profilesData.data[0];
        console.log(`   Name: ${sample.name}`);
        console.log(`   Job: ${sample.jobTitle}`);
        console.log(`   Created: ${new Date(sample.createdAt).toLocaleDateString()}`);
      }
    } else {
      console.log('❌ Protected route access failed:', profilesData.error);
    }
    
    // Test user profile endpoint
    console.log('\n👤 Testing user profile endpoint...');
    const userResponse = await fetch(`${baseURL}/auth/me`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    const userData = await userResponse.json();
    
    if (userResponse.ok) {
      console.log('✅ User profile access successful!');
      console.log(`   Username: ${userData.data.user.username}`);
      console.log(`   Email: ${userData.data.user.email}`);
      console.log(`   Role: ${userData.data.user.role}`);
      console.log(`   Last Login: ${new Date(userData.data.user.lastLogin).toLocaleString()}`);
    } else {
      console.log('❌ User profile access failed:', userData.error);
    }
    
    console.log('\n🎉 API testing complete!');
    console.log('\n💡 Usage Tips:');
    console.log('   • Use the token in Authorization header: Bearer <token>');
    console.log('   • Token expires in 24 hours by default');
    console.log('   • All user-profile endpoints require authentication');
    console.log('   • Admin role required for DELETE operations');
    
  } catch (error) {
    console.error('❌ Test error:', error.message);
    console.log('\n💡 Make sure:');
    console.log('   • Server is running (npm run server)');
    console.log('   • MongoDB is connected');
    console.log('   • You have registered a user account');
  } finally {
    rl.close();
  }
}

// Check if fetch is available (Node.js 18+)
if (typeof fetch === 'undefined') {
  console.log('❌ This script requires Node.js 18+ or you can install node-fetch');
  console.log('💡 Install node-fetch: npm install node-fetch@2');
  console.log('💡 Or upgrade to Node.js 18+');
  process.exit(1);
}

testAPI();
