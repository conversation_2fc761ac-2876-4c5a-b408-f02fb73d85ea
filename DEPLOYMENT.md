# Deployment Guide - Resilient Cape Admin Dashboard

This guide covers deploying the Resilient Cape Admin Dashboard to production environments.

## 🚀 Production Deployment Options

### Option 1: Traditional VPS/Server Deployment

#### Prerequisites
- Ubuntu/CentOS server with root access
- Domain name (optional but recommended)
- SSL certificate (Let's Encrypt recommended)

#### Step 1: Server Setup
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js (using NodeSource repository)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install MongoDB
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list
sudo apt-get update
sudo apt-get install -y mongodb-org

# Start MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod

# Install PM2 for process management
sudo npm install -g pm2
```

#### Step 2: Application Deployment
```bash
# Clone repository
git clone <your-repository-url>
cd resilient-cape-web-admin-dashboard

# Install dependencies
npm install

# Create production environment file
cp .env.example .env
```

#### Step 3: Configure Production Environment
Edit `.env` file:
```env
# Production Configuration
PORT=5000
NODE_ENV=production

# Database (use MongoDB Atlas for better reliability)
MONGODB_URI=mongodb+srv://username:<EMAIL>/resilient-cape-admin

# Security
JWT_SECRET=your-super-secure-64-character-secret-key-for-production-use
JWT_EXPIRES_IN=24h

# CORS (update to your domain)
CORS_ORIGIN=https://yourdomain.com
```

#### Step 4: Start Application
```bash
# Start with PM2
pm2 start server/index.js --name "resilient-cape-admin"

# Save PM2 configuration
pm2 save
pm2 startup

# Check status
pm2 status
```

#### Step 5: Nginx Reverse Proxy (Recommended)
```nginx
# /etc/nginx/sites-available/resilient-cape-admin
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;

    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;

    # Frontend (static files)
    location / {
        root /path/to/resilient-cape-web-admin-dashboard/client;
        try_files $uri $uri/ /index.html;
    }

    # Backend API
    location /api/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/resilient-cape-admin /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### Option 2: Docker Deployment

#### Create Dockerfile
```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy application code
COPY . .

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# Change ownership
RUN chown -R nodejs:nodejs /app
USER nodejs

EXPOSE 5000

CMD ["node", "server/index.js"]
```

#### Create docker-compose.yml
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongo:27017/resilient-cape-admin
      - JWT_SECRET=your-production-jwt-secret
      - CORS_ORIGIN=https://yourdomain.com
    depends_on:
      - mongo
    restart: unless-stopped

  mongo:
    image: mongo:6.0
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./client:/usr/share/nginx/html
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  mongo_data:
```

#### Deploy with Docker
```bash
# Build and start
docker-compose up -d

# Check logs
docker-compose logs -f

# Stop
docker-compose down
```

### Option 3: Cloud Platform Deployment

#### Heroku Deployment
```bash
# Install Heroku CLI
# Create Heroku app
heroku create your-app-name

# Set environment variables
heroku config:set NODE_ENV=production
heroku config:set JWT_SECRET=your-production-secret
heroku config:set MONGODB_URI=your-mongodb-atlas-uri

# Deploy
git push heroku main
```

#### DigitalOcean App Platform
1. Connect GitHub repository
2. Set environment variables in dashboard
3. Configure build and run commands
4. Deploy automatically

## 🔒 Production Security Checklist

### Environment Security
- [ ] Strong JWT_SECRET (64+ characters)
- [ ] Secure MongoDB connection (Atlas recommended)
- [ ] HTTPS enabled with valid SSL certificate
- [ ] Environment variables properly set
- [ ] No sensitive data in code repository

### Application Security
- [ ] Rate limiting implemented
- [ ] Input validation on all endpoints
- [ ] CORS properly configured
- [ ] Error messages don't leak sensitive information
- [ ] File upload restrictions enforced
- [ ] Database queries use parameterization

### Infrastructure Security
- [ ] Firewall configured (only necessary ports open)
- [ ] Regular security updates applied
- [ ] MongoDB authentication enabled
- [ ] Backup strategy implemented
- [ ] Monitoring and logging configured

## 📊 Monitoring and Maintenance

### Application Monitoring
```bash
# PM2 monitoring
pm2 monit

# Check application logs
pm2 logs resilient-cape-admin

# Restart application
pm2 restart resilient-cape-admin
```

### Database Monitoring
```bash
# MongoDB status
sudo systemctl status mongod

# MongoDB logs
sudo tail -f /var/log/mongodb/mongod.log

# Database backup
mongodump --uri="mongodb://localhost:27017/resilient-cape-admin" --out=/backup/$(date +%Y%m%d)
```

### Health Checks
Set up monitoring for:
- Application uptime: `GET /api/health`
- Database connectivity
- Memory and CPU usage
- Disk space
- SSL certificate expiration

## 🔄 Updates and Maintenance

### Regular Updates
```bash
# Pull latest changes
git pull origin main

# Update dependencies
npm update

# Restart application
pm2 restart resilient-cape-admin
```

### Database Maintenance
```bash
# Create database backup
mongodump --uri="$MONGODB_URI" --out=/backup/$(date +%Y%m%d)

# Optimize database
mongo resilient-cape-admin --eval "db.runCommand({compact: 'users'})"
mongo resilient-cape-admin --eval "db.runCommand({compact: 'userprofiles'})"
```

## 🆘 Troubleshooting Production Issues

### Common Production Issues

1. **Application won't start:**
   ```bash
   # Check PM2 logs
   pm2 logs resilient-cape-admin
   
   # Check environment variables
   pm2 env 0
   ```

2. **Database connection issues:**
   ```bash
   # Test MongoDB connection
   mongo --eval "db.adminCommand('ismaster')"
   
   # Check MongoDB logs
   sudo tail -f /var/log/mongodb/mongod.log
   ```

3. **High memory usage:**
   ```bash
   # Monitor memory
   pm2 monit
   
   # Restart if needed
   pm2 restart resilient-cape-admin
   ```

### Emergency Procedures

#### Application Recovery
```bash
# Stop application
pm2 stop resilient-cape-admin

# Restore from backup if needed
git checkout main
git pull origin main

# Restart
pm2 start resilient-cape-admin
```

#### Database Recovery
```bash
# Restore from backup
mongorestore --uri="$MONGODB_URI" /backup/YYYYMMDD/
```

## 📈 Performance Optimization

### Application Performance
- Enable gzip compression
- Implement caching strategies
- Optimize database queries
- Use CDN for static assets

### Database Performance
- Create appropriate indexes
- Monitor slow queries
- Regular database maintenance
- Connection pooling

### Example Nginx Optimization
```nginx
# Enable gzip compression
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

# Enable caching
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

## 📞 Support

For deployment support:
- Check this deployment guide
- Review the main README.md
- Create an issue with deployment tag
- Contact the maintainers

---

**Happy Deploying! 🚀**
