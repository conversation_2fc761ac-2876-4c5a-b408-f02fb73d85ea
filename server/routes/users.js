const express = require('express');
const router = express.Router();
const User = require('../models/User');
const { auth, authorize } = require('../middleware/auth');

// @route   GET /api/users
// @desc    Get all users (super admin only)
// @access  Private (super_admin only)
router.get('/', auth, authorize('super_admin'), async (req, res) => {
  try {
    const users = await User.find().sort({ createdAt: -1 });
    
    res.json({
      success: true,
      count: users.length,
      data: users
    });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch users'
    });
  }
});

// @route   GET /api/users/:id
// @desc    Get single user (super admin only)
// @access  Private (super_admin only)
router.get('/:id', auth, authorize('super_admin'), async (req, res) => {
  try {
    const user = await User.findById(req.params.id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }
    
    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch user'
    });
  }
});

// @route   POST /api/users
// @desc    Create new user (super admin only)
// @access  Private (super_admin only)
router.post('/', auth, authorize('super_admin'), async (req, res) => {
  try {
    const { username, email, password, role } = req.body;

    // Validation
    if (!username || !email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Username, email, and password are required'
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [{ email }, { username }]
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        error: 'User with this email or username already exists'
      });
    }

    // Create new user (cannot create super_admin)
    const allowedRole = role === 'super_admin' ? 'admin' : role;
    
    const user = new User({
      username,
      email,
      password,
      role: allowedRole || 'user',
      isSuperAdmin: false
    });

    await user.save();

    res.status(201).json({
      success: true,
      message: 'User created successfully',
      data: user.toJSON()
    });

  } catch (error) {
    console.error('Create user error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to create user'
    });
  }
});

// @route   PUT /api/users/:id
// @desc    Update user (super admin only)
// @access  Private (super_admin only)
router.put('/:id', auth, authorize('super_admin'), async (req, res) => {
  try {
    const { username, email, role, isActive } = req.body;
    
    const user = await User.findById(req.params.id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Prevent modifying super admin
    if (user.isSuperAdmin) {
      return res.status(403).json({
        success: false,
        error: 'Cannot modify super admin account'
      });
    }

    // Update fields
    if (username) user.username = username;
    if (email) user.email = email;
    if (role && role !== 'super_admin') user.role = role;
    if (typeof isActive === 'boolean') user.isActive = isActive;

    await user.save();

    res.json({
      success: true,
      message: 'User updated successfully',
      data: user.toJSON()
    });

  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to update user'
    });
  }
});

// @route   DELETE /api/users/:id
// @desc    Delete user (super admin only)
// @access  Private (super_admin only)
router.delete('/:id', auth, authorize('super_admin'), async (req, res) => {
  try {
    const user = await User.findById(req.params.id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Prevent deleting super admin
    if (user.isSuperAdmin) {
      return res.status(403).json({
        success: false,
        error: 'Cannot delete super admin account'
      });
    }

    // Prevent deleting yourself
    if (user._id.toString() === req.user.userId) {
      return res.status(403).json({
        success: false,
        error: 'Cannot delete your own account'
      });
    }

    await User.findByIdAndDelete(req.params.id);

    res.json({
      success: true,
      message: 'User deleted successfully'
    });

  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete user'
    });
  }
});

// @route   PUT /api/users/:id/toggle-status
// @desc    Activate/Deactivate user (super admin only)
// @access  Private (super_admin only)
router.put('/:id/toggle-status', auth, authorize('super_admin'), async (req, res) => {
  try {
    const user = await User.findById(req.params.id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Prevent modifying super admin
    if (user.isSuperAdmin) {
      return res.status(403).json({
        success: false,
        error: 'Cannot modify super admin status'
      });
    }

    // Toggle status
    user.isActive = !user.isActive;
    await user.save();

    res.json({
      success: true,
      message: `User ${user.isActive ? 'activated' : 'deactivated'} successfully`,
      data: user.toJSON()
    });

  } catch (error) {
    console.error('Toggle user status error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to toggle user status'
    });
  }
});

// @route   GET /api/users/stats
// @desc    Get user statistics (super admin only)
// @access  Private (super_admin only)
router.get('/stats', auth, authorize('super_admin'), async (req, res) => {
  try {
    const totalUsers = await User.countDocuments();
    const activeUsers = await User.countDocuments({ isActive: true });
    const inactiveUsers = await User.countDocuments({ isActive: false });
    
    const roleStats = await User.aggregate([
      { $group: { _id: '$role', count: { $sum: 1 } } }
    ]);

    const recentUsers = await User.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select('username email role createdAt isActive');

    res.json({
      success: true,
      data: {
        total: totalUsers,
        active: activeUsers,
        inactive: inactiveUsers,
        byRole: roleStats,
        recent: recentUsers
      }
    });

  } catch (error) {
    console.error('Get user stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get user statistics'
    });
  }
});

module.exports = router;
