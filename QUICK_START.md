# 🚀 Quick Start Guide - Bearer Token & External API Access

## ✅ **System Status: READY!**

Both servers are running:
- **Backend API:** `http://localhost:5000` ✅
- **Frontend Dashboard:** `http://localhost:8080` ✅

## 🔑 **Getting Your Bearer Token**

### **Method 1: From Dashboard (Recommended)**
1. Visit: `http://localhost:8080/login.html`
2. Login with your credentials
3. Go to dashboard: `http://localhost:8080/index.html`
4. Scroll to "🔑 API Access Token" section
5. Click "📋 Copy" button to copy your Bearer token

### **Method 2: Using Command Line**
```bash
# Get token quickly
node get-token-simple.js admin admin123

# Copy the token from the output
```

## 🌐 **Using Bearer Token in External Applications**

### **Current Working Example:**
```bash
# This is your current valid token (expires in 24 hours):
curl -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2ODlkYjBkYTAyYzIwZDM1NTRmYzZiNjQiLCJpYXQiOjE3NTUxNzI2ODksImV4cCI6MTc1NTI1OTA4OX0.MRbHB7z1Bitk-zpOemxHxjEyA8GieQ_IGOIDim-COK0" \
  http://localhost:5000/api/user-profiles
```

### **JavaScript Example:**
```javascript
const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2ODlkYjBkYTAyYzIwZDM1NTRmYzZiNjQiLCJpYXQiOjE3NTUxNzI2ODksImV4cCI6MTc1NTI1OTA4OX0.MRbHB7z1Bitk-zpOemxHxjEyA8GieQ_IGOIDim-COK0';

fetch('http://localhost:5000/api/user-profiles', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
})
.then(response => response.json())
.then(data => console.log(data));
```

### **Python Example:**
```python
import requests

token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2ODlkYjBkYTAyYzIwZDM1NTRmYzZiNjQiLCJpYXQiOjE3NTUxNzI2ODksImV4cCI6MTc1NTI1OTA4OX0.MRbHB7z1Bitk-zpOemxHxjEyA8GieQ_IGOIDim-COK0'

headers = {'Authorization': f'Bearer {token}'}
response = requests.get('http://localhost:5000/api/user-profiles', headers=headers)
print(response.json())
```

## 📊 **Available API Endpoints**

### **User Profiles (All authenticated users)**
```bash
# Get all profiles
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:5000/api/user-profiles

# Get single profile
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:5000/api/user-profiles/PROFILE_ID

# Create profile (Admin/Moderator only)
curl -X POST -H "Authorization: Bearer YOUR_TOKEN" \
  -F "name=John Doe" \
  -F "jobTitle=Developer" \
  -F "image=@photo.jpg" \
  http://localhost:5000/api/user-profiles

# Update profile (Admin/Moderator only)
curl -X PUT -H "Authorization: Bearer YOUR_TOKEN" \
  -F "name=John Doe Updated" \
  http://localhost:5000/api/user-profiles/PROFILE_ID

# Delete profile (Admin only)
curl -X DELETE -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:5000/api/user-profiles/PROFILE_ID
```

### **User Management (Super Admin only)**
```bash
# Get all users
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:5000/api/users

# Create new user
curl -X POST -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"username":"newuser","email":"<EMAIL>","password":"password123","role":"moderator"}' \
  http://localhost:5000/api/users

# Activate/Deactivate user
curl -X PUT -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:5000/api/users/USER_ID/toggle-status
```

### **Authentication Endpoints**
```bash
# Login (get new token)
curl -X POST -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}' \
  http://localhost:5000/api/auth/login

# Verify token
curl -X POST -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:5000/api/auth/verify-token

# Get current user info
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:5000/api/auth/me
```

## 🎯 **Dashboard Features**

### **Modern Design Elements:**
- **Beautiful gradients** and professional styling
- **Interactive cards** with hover animations
- **Responsive layout** for all devices
- **Real-time statistics** and data display

### **Bearer Token Section:**
- **Prominent display** of your current Bearer token
- **One-click copy** functionality
- **Live code examples** with your actual token
- **Token expiration** information

### **User Management (Super Admin):**
- **Create new users** with different roles
- **Activate/deactivate** user accounts
- **View user statistics** and activity
- **Role-based permissions** control

## 🔐 **User Roles & Permissions**

### **Super Admin (First registered user):**
- ✅ Full access to everything
- ✅ Create/edit/delete users
- ✅ Activate/deactivate accounts
- ✅ Access user management page
- ✅ All profile operations

### **Admin:**
- ✅ View/create/edit/delete profiles
- ✅ Access dashboard statistics
- ❌ Cannot manage other users

### **Moderator:**
- ✅ View/create/edit profiles
- ❌ Cannot delete profiles
- ❌ Cannot manage users

### **User:**
- ✅ View profiles only
- ❌ Cannot create/edit/delete
- ❌ Cannot manage users

## 🌐 **URLs Summary**

### **Frontend Pages:**
- **Dashboard:** `http://localhost:8080/index.html`
- **Login:** `http://localhost:8080/login.html`
- **Register:** `http://localhost:8080/register.html`
- **User Management:** `http://localhost:8080/users.html` (Super Admin only)

### **API Endpoints:**
- **Base URL:** `http://localhost:5000/api`
- **Health Check:** `http://localhost:5000/api/health`
- **Authentication:** `http://localhost:5000/api/auth/*`
- **User Profiles:** `http://localhost:5000/api/user-profiles/*`
- **User Management:** `http://localhost:5000/api/users/*`

## 🛠️ **Quick Commands**

```bash
# Get Bearer token
node get-token-simple.js admin admin123

# Test API with token
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:5000/api/user-profiles

# Start servers
npm run server  # Backend
cd client && python3 -m http.server 8080  # Frontend

# Check server status
curl http://localhost:5000/api/health
curl http://localhost:8080/index.html
```

## 🎉 **What You Can Do Now:**

1. **✅ Access beautiful modern dashboard** at `http://localhost:8080/index.html`
2. **✅ Copy Bearer token** from the dashboard with one click
3. **✅ Use token in external applications** for API access
4. **✅ Manage users** (if you're the Super Admin)
5. **✅ Upload and manage profiles** with real-time statistics
6. **✅ Deploy to Vercel** using the provided configuration

## 🔧 **Troubleshooting**

### **JavaScript Error Fixed:**
- ✅ Removed duplicate `authManager` declarations
- ✅ Proper script loading order
- ✅ Clean error-free console

### **CORS Fixed:**
- ✅ Server accepts requests from `http://localhost:8080`
- ✅ Flexible CORS configuration for development and production

### **File Serving Fixed:**
- ✅ Frontend server running from correct `client` directory
- ✅ All HTML, CSS, and JS files accessible

---

## 🎊 **Your System is Complete and Working!**

**Dashboard:** `http://localhost:8080/index.html`
**Bearer Token:** Displayed prominently in the dashboard
**API Access:** Working perfectly with proper authentication
**Modern Design:** Professional, responsive, and beautiful

**Ready for production deployment to Vercel!** 🚀
