# Resilient Cape Admin Dashboard

A secure web-based admin dashboard system for managing user profiles with JWT authentication, Bearer token API access, and MongoDB integration. Features complete user authentication, role-based access control, and protected API endpoints.

## 🚀 Features

### Authentication & Security
- ✅ **JWT Authentication**: Secure login/logout with JSON Web Tokens
- ✅ **Bearer Token API**: All API calls protected with Bearer token authentication
- ✅ **Role-Based Access**: Admin, Moderator, and User roles with different permissions
- ✅ **Password Security**: Bcrypt hashing with salt rounds
- ✅ **Session Management**: Automatic token validation and renewal
- ✅ **Protected Routes**: All sensitive endpoints require authentication

### User Profile Management
- ✅ **CRUD Operations**: Create, read, update, and delete user profiles
- ✅ **Image Upload**: Upload JPG, PNG, and WebP images (max 5MB)
- ✅ **Base64 Storage**: Images automatically converted to base64 and stored in MongoDB
- ✅ **Real-time Updates**: Instant profile listing updates
- ✅ **Validation**: Comprehensive form and file validation

### User Interface
- ✅ **Professional Login/Register Pages**: Modern authentication interface
- ✅ **Responsive Design**: Mobile-friendly admin dashboard
- ✅ **Navigation Bar**: User info display with logout functionality
- ✅ **Error Handling**: User-friendly error messages and loading states

## 🛠️ Tech Stack

### Backend
- **Node.js** with Express.js framework
- **MongoDB** with Mongoose ODM
- **JWT** for authentication tokens
- **bcryptjs** for password hashing
- **Multer** for file upload handling
- **CORS** for cross-origin requests
- **dotenv** for environment configuration

### Frontend
- **HTML5** with semantic markup
- **CSS3** with responsive design and animations
- **Vanilla JavaScript** with modern ES6+ features
- **Local Storage** for token management

## 📁 Project Structure

```
resilient-cape-web-admin-dashboard/
├── server/                     # Backend API
│   ├── config/
│   │   └── database.js        # MongoDB connection setup
│   ├── middleware/
│   │   └── auth.js            # JWT authentication middleware
│   ├── models/
│   │   ├── User.js            # User authentication model
│   │   └── UserProfile.js     # User profile data model
│   ├── routes/
│   │   ├── auth.js            # Authentication endpoints
│   │   └── userProfiles.js    # User profile CRUD endpoints
│   └── index.js               # Express server configuration
├── client/                     # Frontend application
│   ├── index.html             # Main dashboard page
│   ├── login.html             # Login page
│   ├── register.html          # Registration page
│   ├── app.js                 # Dashboard functionality
│   ├── auth.js                # Authentication manager
│   ├── styles.css             # Main application styles
│   └── auth.css               # Authentication page styles
├── .env                       # Environment variables (create from .env.example)
├── .env.example               # Environment template
├── package.json               # Node.js dependencies
├── package-lock.json          # Dependency lock file
└── README.md                  # Project documentation
```

## 📋 Prerequisites

- **Node.js** (v14 or higher)
- **MongoDB** (local installation or cloud instance)
- **Modern web browser** (Chrome, Firefox, Safari, Edge)
- **Git** for version control

## 🛠️ Installation & Setup

### 1. Clone and Install Dependencies

```bash
git clone <repository-url>
cd resilient-cape-web-admin-dashboard
npm install
```

### 2. Environment Configuration

Create your environment file:
```bash
cp .env.example .env
```

Edit `.env` with your configuration:
```env
# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/resilient-cape-admin

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# CORS Configuration
CORS_ORIGIN=http://localhost:8080
```

### 3. Database Setup

**Option A: Local MongoDB**
```bash
# Install MongoDB (Ubuntu/Debian)
sudo apt update
sudo apt install mongodb

# Start MongoDB service
sudo systemctl start mongod
sudo systemctl enable mongod
```

**Option B: MongoDB Docker**
```bash
docker run -d -p 27017:27017 --name mongodb mongo:latest
```

**Option C: MongoDB Atlas (Cloud)**
- Create account at [MongoDB Atlas](https://www.mongodb.com/atlas)
- Create cluster and get connection string
- Update `MONGODB_URI` in `.env`

## 🚀 Running the Application

### Development Mode

1. **Start the backend server:**
   ```bash
   npm run server
   ```
   Server will run on `http://localhost:5000`

2. **Start the frontend (in a new terminal):**
   ```bash
   cd client
   python3 -m http.server 8080
   ```
   Frontend will be available at `http://localhost:8080`

### Production Mode

```bash
npm run build
npm start
```

## 🔐 Authentication Flow

### 1. First Time Setup

1. **Create Admin Account:**
   - Visit `http://localhost:8080/register.html`
   - Fill in admin credentials:
     - Username: `admin`
     - Email: `<EMAIL>`
     - Password: `admin123` (or your choice)
     - Role: `Admin`
   - Click "Create Account"

2. **Login:**
   - Visit `http://localhost:8080/login.html`
   - Enter your credentials
   - Get redirected to dashboard with navigation bar

### 2. API Authentication

All API endpoints (except auth routes) require Bearer token authentication:

```javascript
// Example API call with Bearer token
const token = localStorage.getItem('authToken');

fetch('http://localhost:5000/api/user-profiles', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
})
```

## 📚 API Documentation

### Authentication Endpoints

| Method | Endpoint | Description | Access |
|--------|----------|-------------|---------|
| POST | `/api/auth/register` | Register new admin user | Public |
| POST | `/api/auth/login` | Login and get JWT token | Public |
| GET | `/api/auth/me` | Get current user profile | Private |
| POST | `/api/auth/verify-token` | Verify token validity | Private |
| POST | `/api/auth/logout` | Logout user | Private |

### User Profile Endpoints

| Method | Endpoint | Description | Access | Roles |
|--------|----------|-------------|---------|-------|
| GET | `/api/user-profiles` | Get all profiles | Private | All |
| GET | `/api/user-profiles/:id` | Get single profile | Private | All |
| POST | `/api/user-profiles` | Create new profile | Private | Admin, Moderator |
| PUT | `/api/user-profiles/:id` | Update profile | Private | Admin, Moderator |
| DELETE | `/api/user-profiles/:id` | Delete profile | Private | Admin only |

### Example API Requests

#### Login
```bash
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

#### Get Profiles with Bearer Token
```bash
curl -X GET http://localhost:5000/api/user-profiles \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

#### Create Profile with Bearer Token
```bash
curl -X POST http://localhost:5000/api/user-profiles \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -F "name=John Doe" \
  -F "jobTitle=Developer" \
  -F "image=@profile.jpg"
```

## 🗄️ Database Schema

### User Collection (Authentication)
```javascript
{
  username: String (unique, required, 3-50 chars),
  email: String (unique, required, valid email),
  password: String (hashed with bcrypt, min 6 chars),
  role: String (admin/moderator/user, default: admin),
  isActive: Boolean (default: true),
  lastLogin: Date,
  createdAt: Date (auto-generated),
  updatedAt: Date (auto-updated)
}
```

### UserProfile Collection (Profile Data)
```javascript
{
  name: String (required, max 100 chars),
  jobTitle: String (required, max 100 chars),
  image: String (base64 encoded, required),
  createdAt: Date (auto-generated)
}
```

## 🎯 User Roles & Permissions

| Role | View Profiles | Create Profiles | Update Profiles | Delete Profiles |
|------|---------------|-----------------|-----------------|-----------------|
| **Admin** | ✅ | ✅ | ✅ | ✅ |
| **Moderator** | ✅ | ✅ | ✅ | ❌ |
| **User** | ✅ | ❌ | ❌ | ❌ |

## 🔒 Security Features

- **Password Security:** Bcrypt hashing with 12 salt rounds
- **JWT Tokens:** Secure token generation with configurable expiration
- **Role-Based Access:** Different permission levels for different user types
- **Input Validation:** Server-side validation for all inputs
- **CORS Protection:** Configured for specific origins
- **File Upload Security:** Type and size validation for images
- **Session Management:** Automatic token validation and cleanup

## 🧪 Testing

The system includes comprehensive authentication testing. All core features tested:

- ✅ User registration with validation
- ✅ User login with credential verification
- ✅ JWT token generation and verification
- ✅ Protected route access control
- ✅ Invalid token rejection
- ✅ Role-based permissions
- ✅ Session management

## 💻 Usage Examples

### Frontend JavaScript

```javascript
// Initialize authentication manager
const authManager = new AuthManager();

// Login user
try {
  await authManager.login('admin', 'password123');
  console.log('Login successful');
} catch (error) {
  console.error('Login failed:', error.message);
}

// Make authenticated API call
const response = await fetch('/api/user-profiles', {
  headers: authManager.getAuthHeaders()
});

// Logout
await authManager.logout();
```

### Backend API Protection

```javascript
// Protect route with authentication
router.get('/protected', auth, (req, res) => {
  res.json({
    message: 'Access granted',
    user: req.user
  });
});

// Protect route with role-based access
router.delete('/admin-only', auth, authorize('admin'), (req, res) => {
  res.json({ message: 'Admin access granted' });
});
```

## 🚀 Quick Start Guide

### Step 1: Setup and Start
```bash
# Install dependencies
npm install

# Start backend server
npm run server
```

### Step 2: Start Frontend
```bash
# In a new terminal
cd client
python3 -m http.server 8080
```

### Step 3: Create Admin Account
1. Visit `http://localhost:8080/register.html`
2. Create your admin account
3. Get automatically logged in

### Step 4: Start Managing Profiles
1. Add user profiles through the dashboard
2. All data is stored in MongoDB
3. Use Bearer tokens for API access

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `PORT` | Server port | 5000 | No |
| `MONGODB_URI` | MongoDB connection string | mongodb://localhost:27017/resilient-cape-admin | Yes |
| `JWT_SECRET` | JWT signing secret | your-secret-key | Yes |
| `JWT_EXPIRES_IN` | Token expiration time | 24h | No |
| `CORS_ORIGIN` | Allowed CORS origin | http://localhost:8080 | No |
| `NODE_ENV` | Environment mode | development | No |

### JWT Token Configuration

- **Default Expiration:** 24 hours
- **Algorithm:** HS256
- **Storage:** Client-side localStorage
- **Format:** `Bearer <token>`

## 🐛 Troubleshooting

### Common Issues

1. **"No token provided" error:**
   - Ensure you're logged in
   - Check if token exists in browser localStorage
   - Verify token format: `Bearer <token>`

2. **Database connection failed:**
   - Check MongoDB is running: `sudo systemctl status mongod`
   - Verify MONGODB_URI in .env file
   - Check network connectivity

3. **CORS errors:**
   - Update CORS_ORIGIN in .env to match frontend URL
   - Ensure frontend and backend URLs are correct

4. **Login page not loading:**
   - Check if frontend server is running on port 8080
   - Verify all HTML/CSS/JS files are in client directory

### Debug Mode

Enable debug logging:
```env
NODE_ENV=development
DEBUG=true
```

## 🚀 Deployment

### Production Checklist

1. **Security:**
   - Generate strong `JWT_SECRET` (32+ characters)
   - Use HTTPS in production
   - Set secure environment variables
   - Configure rate limiting

2. **Database:**
   - Use MongoDB Atlas or production MongoDB instance
   - Set up database backups
   - Configure monitoring

3. **Environment:**
   - Set `NODE_ENV=production`
   - Configure proper `CORS_ORIGIN`
   - Set up process manager (PM2)

### Example Production Deployment

```bash
# Install PM2
npm install -g pm2

# Start application with PM2
pm2 start server/index.js --name "resilient-cape-admin"

# Save PM2 configuration
pm2 save
pm2 startup
```

## 📖 API Response Examples

### Successful Login Response
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "_id": "689db0da02c20d3554fc6b64",
      "username": "admin",
      "email": "<EMAIL>",
      "role": "admin",
      "isActive": true,
      "lastLogin": "2025-08-14T09:48:27.016Z"
    }
  }
}
```

### Protected Route Access
```json
{
  "success": true,
  "count": 2,
  "data": [
    {
      "_id": "689db0da02c20d3554fc6b65",
      "name": "John Doe",
      "jobTitle": "Software Developer",
      "image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABA...",
      "createdAt": "2025-08-14T10:15:30.123Z"
    }
  ]
}
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Make your changes
4. Add tests if applicable
5. Commit changes: `git commit -m 'Add new feature'`
6. Push to branch: `git push origin feature/new-feature`
7. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the troubleshooting section above
- Review the API documentation
- Create an issue in the repository

---

## 🎯 Default Credentials

**Test Admin Account:**
- Username: `admin`
- Email: `<EMAIL>`
- Password: `admin123`
- Role: `admin`

## 🌐 URLs

- **Frontend Dashboard:** `http://localhost:8080/index.html`
- **Login Page:** `http://localhost:8080/login.html`
- **Registration Page:** `http://localhost:8080/register.html`
- **Backend API:** `http://localhost:5000/api`
- **Health Check:** `http://localhost:5000/api/health`

## 🔑 Bearer Token Usage

After login, use the JWT token for all API calls:

```javascript
// Get token from login response or localStorage
const token = localStorage.getItem('authToken');

// Include in API requests
const headers = {
  'Authorization': `Bearer ${token}`,
  'Content-Type': 'application/json'
};

// Example: Get all profiles
fetch('http://localhost:5000/api/user-profiles', { headers })
  .then(response => response.json())
  .then(data => console.log(data));
```

---

**🎉 Your secure admin dashboard with Bearer token authentication is ready to use!**
