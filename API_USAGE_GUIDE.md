# API Usage Guide - External Applications

This guide shows how to use the Resilient Cape Admin Dashboard API from external applications using Bearer token authentication.

## 🔑 Getting Your Bearer Token

### Method 1: Using the Get Token Script
```bash
# Run the token script
node get-token.js

# Follow the prompts to enter your credentials
# Copy the Bearer token from the output
```

### Method 2: Using curl
```bash
# Login and get token
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "your-username", "password": "your-password"}'

# Response will include the token:
# {
#   "success": true,
#   "data": {
#     "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
#     "user": { ... }
#   }
# }
```

### Method 3: Using JavaScript/Node.js
```javascript
const fetch = require('node-fetch'); // or use built-in fetch in Node.js 18+

async function getToken() {
  const response = await fetch('http://localhost:5000/api/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      username: 'your-username',
      password: 'your-password'
    })
  });
  
  const data = await response.json();
  return data.data.token;
}
```

## 📊 API Endpoints for External Use

### 1. Get All User Profiles

```bash
# Using curl
curl -X GET http://localhost:5000/api/user-profiles \
  -H "Authorization: Bearer YOUR_ACTUAL_TOKEN_HERE"

# Response:
# {
#   "success": true,
#   "count": 2,
#   "data": [
#     {
#       "_id": "...",
#       "name": "John Doe",
#       "jobTitle": "Developer",
#       "image": "data:image/jpeg;base64,...",
#       "createdAt": "2025-08-14T10:30:00.000Z"
#     }
#   ]
# }
```

### 2. Get Single User Profile

```bash
curl -X GET http://localhost:5000/api/user-profiles/PROFILE_ID \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. Create New Profile (Admin/Moderator only)

```bash
# With image file
curl -X POST http://localhost:5000/api/user-profiles \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "name=Jane Smith" \
  -F "jobTitle=Designer" \
  -F "image=@path/to/image.jpg"

# Response:
# {
#   "success": true,
#   "data": { ... },
#   "message": "Profile created successfully"
# }
```

### 4. Update Profile (Admin/Moderator only)

```bash
curl -X PUT http://localhost:5000/api/user-profiles/PROFILE_ID \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "name=Jane Smith Updated" \
  -F "jobTitle=Senior Designer"
```

### 5. Delete Profile (Admin only)

```bash
curl -X DELETE http://localhost:5000/api/user-profiles/PROFILE_ID \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 👥 User Management API (Super Admin Only)

### Get All Users
```bash
curl -X GET http://localhost:5000/api/users \
  -H "Authorization: Bearer YOUR_SUPER_ADMIN_TOKEN"
```

### Create New User
```bash
curl -X POST http://localhost:5000/api/users \
  -H "Authorization: Bearer YOUR_SUPER_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "email": "<EMAIL>",
    "password": "password123",
    "role": "moderator"
  }'
```

### Activate/Deactivate User
```bash
curl -X PUT http://localhost:5000/api/users/USER_ID/toggle-status \
  -H "Authorization: Bearer YOUR_SUPER_ADMIN_TOKEN"
```

## 🔧 Integration Examples

### Python Example
```python
import requests

class ResilientCapeAPI:
    def __init__(self, base_url, username, password):
        self.base_url = base_url
        self.token = None
        self.login(username, password)
    
    def login(self, username, password):
        response = requests.post(f"{self.base_url}/auth/login", json={
            "username": username,
            "password": password
        })
        
        if response.status_code == 200:
            data = response.json()
            self.token = data['data']['token']
        else:
            raise Exception("Login failed")
    
    def get_headers(self):
        return {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json"
        }
    
    def get_profiles(self):
        response = requests.get(
            f"{self.base_url}/user-profiles",
            headers=self.get_headers()
        )
        return response.json()
    
    def create_profile(self, name, job_title, image_path):
        with open(image_path, 'rb') as image_file:
            files = {'image': image_file}
            data = {'name': name, 'jobTitle': job_title}
            
            response = requests.post(
                f"{self.base_url}/user-profiles",
                headers={"Authorization": f"Bearer {self.token}"},
                data=data,
                files=files
            )
        return response.json()

# Usage
api = ResilientCapeAPI("http://localhost:5000/api", "admin", "admin123")
profiles = api.get_profiles()
print(f"Found {profiles['count']} profiles")
```

### Node.js Example
```javascript
const fetch = require('node-fetch');
const FormData = require('form-data');
const fs = require('fs');

class ResilientCapeAPI {
    constructor(baseUrl) {
        this.baseUrl = baseUrl;
        this.token = null;
    }
    
    async login(username, password) {
        const response = await fetch(`${this.baseUrl}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ username, password })
        });
        
        const data = await response.json();
        if (response.ok) {
            this.token = data.data.token;
        } else {
            throw new Error(data.error);
        }
    }
    
    getHeaders() {
        return {
            'Authorization': `Bearer ${this.token}`,
            'Content-Type': 'application/json'
        };
    }
    
    async getProfiles() {
        const response = await fetch(`${this.baseUrl}/user-profiles`, {
            headers: this.getHeaders()
        });
        return response.json();
    }
    
    async createProfile(name, jobTitle, imagePath) {
        const form = new FormData();
        form.append('name', name);
        form.append('jobTitle', jobTitle);
        form.append('image', fs.createReadStream(imagePath));
        
        const response = await fetch(`${this.baseUrl}/user-profiles`, {
            method: 'POST',
            headers: { 'Authorization': `Bearer ${this.token}` },
            body: form
        });
        
        return response.json();
    }
}

// Usage
async function example() {
    const api = new ResilientCapeAPI('http://localhost:5000/api');
    await api.login('admin', 'admin123');
    
    const profiles = await api.getProfiles();
    console.log(`Found ${profiles.count} profiles`);
}
```

### PHP Example
```php
<?php
class ResilientCapeAPI {
    private $baseUrl;
    private $token;
    
    public function __construct($baseUrl) {
        $this->baseUrl = $baseUrl;
    }
    
    public function login($username, $password) {
        $data = json_encode([
            'username' => $username,
            'password' => $password
        ]);
        
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => 'Content-Type: application/json',
                'content' => $data
            ]
        ]);
        
        $response = file_get_contents($this->baseUrl . '/auth/login', false, $context);
        $result = json_decode($response, true);
        
        if ($result['success']) {
            $this->token = $result['data']['token'];
        } else {
            throw new Exception($result['error']);
        }
    }
    
    public function getProfiles() {
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => 'Authorization: Bearer ' . $this->token
            ]
        ]);
        
        $response = file_get_contents($this->baseUrl . '/user-profiles', false, $context);
        return json_decode($response, true);
    }
}

// Usage
$api = new ResilientCapeAPI('http://localhost:5000/api');
$api->login('admin', 'admin123');
$profiles = $api->getProfiles();
echo "Found " . $profiles['count'] . " profiles\n";
?>
```

## 🔒 Authentication Requirements

### Token Format
All API requests must include the Authorization header:
```
Authorization: Bearer YOUR_JWT_TOKEN_HERE
```

### Token Expiration
- Default: 24 hours
- Get new token by logging in again
- Check token validity with `/api/auth/verify-token`

### Role-Based Access

| Endpoint | Super Admin | Admin | Moderator | User |
|----------|-------------|-------|-----------|------|
| GET /user-profiles | ✅ | ✅ | ✅ | ✅ |
| POST /user-profiles | ✅ | ✅ | ✅ | ❌ |
| PUT /user-profiles | ✅ | ✅ | ✅ | ❌ |
| DELETE /user-profiles | ✅ | ✅ | ❌ | ❌ |
| GET /users | ✅ | ❌ | ❌ | ❌ |
| POST /users | ✅ | ❌ | ❌ | ❌ |
| PUT /users | ✅ | ❌ | ❌ | ❌ |
| DELETE /users | ✅ | ❌ | ❌ | ❌ |

## 🚨 Common Mistakes

### ❌ Wrong: Using JWT Secret as Token
```bash
# This is WRONG - don't use the JWT secret
curl -H "Authorization: Bearer your-super-secret-jwt-key-change-this-in-production"
```

### ✅ Correct: Using Actual JWT Token
```bash
# This is CORRECT - use the token from login response
curl -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

### ❌ Wrong: Missing Bearer Prefix
```bash
curl -H "Authorization: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

### ✅ Correct: With Bearer Prefix
```bash
curl -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

## 🔄 Token Refresh Strategy

```javascript
class APIClient {
    constructor(baseUrl) {
        this.baseUrl = baseUrl;
        this.token = localStorage.getItem('authToken');
    }
    
    async makeRequest(endpoint, options = {}) {
        let response = await fetch(`${this.baseUrl}${endpoint}`, {
            ...options,
            headers: {
                ...options.headers,
                'Authorization': `Bearer ${this.token}`
            }
        });
        
        // If token expired, try to refresh
        if (response.status === 401) {
            await this.refreshToken();
            
            // Retry request with new token
            response = await fetch(`${this.baseUrl}${endpoint}`, {
                ...options,
                headers: {
                    ...options.headers,
                    'Authorization': `Bearer ${this.token}`
                }
            });
        }
        
        return response;
    }
    
    async refreshToken() {
        // Implement token refresh logic
        // For now, redirect to login
        window.location.href = '/login.html';
    }
}
```

## 📱 Mobile App Integration

### React Native Example
```javascript
import AsyncStorage from '@react-native-async-storage/async-storage';

class ResilientCapeAPI {
    constructor(baseUrl) {
        this.baseUrl = baseUrl;
    }
    
    async login(username, password) {
        const response = await fetch(`${this.baseUrl}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ username, password })
        });
        
        const data = await response.json();
        if (response.ok) {
            await AsyncStorage.setItem('authToken', data.data.token);
            return data.data.token;
        } else {
            throw new Error(data.error);
        }
    }
    
    async getProfiles() {
        const token = await AsyncStorage.getItem('authToken');
        const response = await fetch(`${this.baseUrl}/user-profiles`, {
            headers: { 'Authorization': `Bearer ${token}` }
        });
        return response.json();
    }
}
```

## 🌐 Production URLs

When deployed to Vercel, update your API base URL:

```javascript
// Development
const API_BASE_URL = 'http://localhost:5000/api';

// Production
const API_BASE_URL = 'https://your-vercel-app.vercel.app/api';

// Dynamic (recommended)
const API_BASE_URL = window.location.hostname === 'localhost' 
    ? 'http://localhost:5000/api'
    : `${window.location.origin}/api`;
```

## 🧪 Testing Your Integration

### Quick Test Script
```bash
#!/bin/bash

# Set your credentials
USERNAME="admin"
PASSWORD="admin123"
API_URL="http://localhost:5000/api"

# Login and get token
echo "🔐 Logging in..."
TOKEN=$(curl -s -X POST $API_URL/auth/login \
  -H "Content-Type: application/json" \
  -d "{\"username\":\"$USERNAME\",\"password\":\"$PASSWORD\"}" | \
  jq -r '.data.token')

if [ "$TOKEN" = "null" ]; then
    echo "❌ Login failed"
    exit 1
fi

echo "✅ Login successful"
echo "🎫 Token: ${TOKEN:0:50}..."

# Test API call
echo "📊 Getting profiles..."
curl -s -H "Authorization: Bearer $TOKEN" \
  $API_URL/user-profiles | jq '.'

echo "🎉 API test complete!"
```

## 📋 Error Handling

### Common HTTP Status Codes

| Code | Meaning | Action |
|------|---------|--------|
| 200 | Success | Process response data |
| 201 | Created | Resource created successfully |
| 400 | Bad Request | Check request format/data |
| 401 | Unauthorized | Get new token or login |
| 403 | Forbidden | Check user permissions |
| 404 | Not Found | Resource doesn't exist |
| 500 | Server Error | Check server logs |

### Error Response Format
```json
{
  "success": false,
  "error": "Descriptive error message"
}
```

### Success Response Format
```json
{
  "success": true,
  "data": { ... },
  "message": "Optional success message"
}
```

## 🔄 Rate Limiting (Future Enhancement)

Currently no rate limiting is implemented, but consider:
- Max 100 requests per minute per IP
- Max 1000 requests per hour per user
- Implement exponential backoff for retries

## 🛡️ Security Best Practices

1. **Store tokens securely** (not in plain text)
2. **Use HTTPS in production**
3. **Implement token refresh** before expiration
4. **Handle 401 errors** gracefully
5. **Don't log sensitive data**
6. **Validate all responses**

---

**🎯 You now have everything needed to integrate with external applications using Bearer token authentication!**
