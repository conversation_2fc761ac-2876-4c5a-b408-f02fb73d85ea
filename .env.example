# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
# Local MongoDB (default)
MONGODB_URI=mongodb://localhost:27017/resilient-cape-admin

# Production MongoDB Atlas example:
# MONGODB_URI=mongodb+srv://username:<EMAIL>/resilient-cape-admin

# JWT Configuration
# IMPORTANT: Change this to a strong secret in production (32+ characters)
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# CORS Configuration
# Update this to match your frontend URL
CORS_ORIGIN=http://localhost:8080

# File Upload Configuration
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=image/jpeg,image/jpg,image/png,image/webp

# Debug Configuration (optional)
DEBUG=false
