// User Management JavaScript
const API_BASE_URL = window.CONFIG ? window.CONFIG.API_BASE_URL : 'http://localhost:5000/api';

// DOM Elements
const createUserForm = document.getElementById('createUserForm');
const createUserBtn = document.getElementById('createUserBtn');
const refreshUsersBtn = document.getElementById('refreshUsersBtn');
const usersLoadingIndicator = document.getElementById('usersLoadingIndicator');
const usersList = document.getElementById('usersList');
const userModal = document.getElementById('userModal');
const editUserForm = document.getElementById('editUserForm');

// Statistics elements
const totalUsersEl = document.getElementById('totalUsers');
const activeUsersEl = document.getElementById('activeUsers');
const inactiveUsersEl = document.getElementById('inactiveUsers');
const totalProfilesEl = document.getElementById('totalProfiles');

// Initialize authentication manager
// authManager is already declared in auth.js

document.addEventListener('DOMContentLoaded', async () => {
    // Check authentication - authManager is already initialized in auth.js
    if (!authManager) {
        window.location.href = 'login.html';
        return;
    }
    
    if (!authManager.isAuthenticated()) {
        window.location.href = 'login.html';
        return;
    }

    // Verify token and check if super admin
    const isValid = await authManager.verifyToken();
    if (!isValid) {
        authManager.clearAuth();
        window.location.href = 'login.html';
        return;
    }

    // Check if user is super admin
    try {
        const currentUser = await authManager.getCurrentUser();
        if (currentUser.role !== 'super_admin') {
            alert('Access denied. Super admin privileges required.');
            window.location.href = 'index.html';
            return;
        }
    } catch (error) {
        console.error('Error checking user role:', error);
        window.location.href = 'login.html';
        return;
    }

    // Add navigation
    addDashboardNavigation();
    
    // Load initial data
    loadUserStats();
    loadUsers();
});

// Event Listeners
createUserForm.addEventListener('submit', handleCreateUser);
refreshUsersBtn.addEventListener('click', loadUsers);
editUserForm.addEventListener('submit', handleEditUser);

// Modal controls
document.getElementById('closeUserModal').addEventListener('click', () => {
    userModal.style.display = 'none';
});

document.getElementById('cancelEdit').addEventListener('click', () => {
    userModal.style.display = 'none';
});

window.addEventListener('click', (e) => {
    if (e.target === userModal) {
        userModal.style.display = 'none';
    }
});

// Get authentication headers
function getAuthHeaders() {
    return {
        'Authorization': `Bearer ${authManager.token}`,
        'Content-Type': 'application/json'
    };
}

// Load user statistics
async function loadUserStats() {
    try {
        // Get user stats
        const usersResponse = await fetch(`${API_BASE_URL}/users/stats`, {
            headers: getAuthHeaders()
        });

        if (usersResponse.ok) {
            const usersData = await usersResponse.json();
            const stats = usersData.data;
            
            totalUsersEl.textContent = stats.total;
            activeUsersEl.textContent = stats.active;
            inactiveUsersEl.textContent = stats.inactive;
        }

        // Get profile count
        const profilesResponse = await fetch(`${API_BASE_URL}/user-profiles`, {
            headers: getAuthHeaders()
        });

        if (profilesResponse.ok) {
            const profilesData = await profilesResponse.json();
            totalProfilesEl.textContent = profilesData.count;
        }

    } catch (error) {
        console.error('Error loading stats:', error);
    }
}

// Load all users
async function loadUsers() {
    try {
        usersLoadingIndicator.style.display = 'inline';
        refreshUsersBtn.disabled = true;

        const response = await fetch(`${API_BASE_URL}/users`, {
            headers: getAuthHeaders()
        });

        const result = await response.json();

        if (response.ok) {
            displayUsers(result.data);
        } else {
            showMessage('Failed to load users: ' + result.error, 'error');
        }
    } catch (error) {
        showMessage('Network error: ' + error.message, 'error');
    } finally {
        usersLoadingIndicator.style.display = 'none';
        refreshUsersBtn.disabled = false;
    }
}

// Display users in grid
function displayUsers(users) {
    if (users.length === 0) {
        usersList.innerHTML = '<p class="loading">No users found.</p>';
        return;
    }

    usersList.innerHTML = users.map(user => `
        <div class="user-card ${!user.isActive ? 'inactive' : ''}">
            <div class="user-header">
                <h3 class="user-name">${user.username}</h3>
                <span class="user-role ${user.role}">${user.role.replace('_', ' ')}</span>
            </div>
            <div class="user-details">
                <p><strong>Email:</strong> ${user.email}</p>
                <p><strong>Status:</strong> 
                    <span class="status ${user.isActive ? 'active' : 'inactive'}">
                        ${user.isActive ? 'Active' : 'Inactive'}
                    </span>
                </p>
                <p><strong>Created:</strong> ${formatDate(user.createdAt)}</p>
                <p><strong>Last Login:</strong> ${user.lastLogin ? formatDate(user.lastLogin) : 'Never'}</p>
            </div>
            <div class="user-actions">
                ${!user.isSuperAdmin ? `
                    <button class="btn-small" onclick="editUser('${user._id}')">Edit</button>
                    <button class="btn-small ${user.isActive ? 'btn-warning' : 'btn-success'}" 
                            onclick="toggleUserStatus('${user._id}', ${user.isActive})">
                        ${user.isActive ? 'Deactivate' : 'Activate'}
                    </button>
                    <button class="btn-small btn-danger" onclick="deleteUser('${user._id}')">Delete</button>
                ` : `
                    <span class="super-admin-badge">Super Admin</span>
                `}
            </div>
        </div>
    `).join('');
}

// Create new user
async function handleCreateUser(e) {
    e.preventDefault();
    
    const formData = new FormData(createUserForm);
    const userData = {
        username: formData.get('username').trim(),
        email: formData.get('email').trim(),
        password: formData.get('password'),
        role: formData.get('role')
    };

    if (!userData.username || !userData.email || !userData.password) {
        showMessage('Please fill in all required fields', 'error');
        return;
    }

    try {
        setLoading('createUserBtn', true);

        const response = await fetch(`${API_BASE_URL}/users`, {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify(userData)
        });

        const result = await response.json();

        if (response.ok) {
            showMessage('User created successfully!', 'success');
            createUserForm.reset();
            loadUsers();
            loadUserStats();
        } else {
            showMessage(result.error || 'Failed to create user', 'error');
        }
    } catch (error) {
        showMessage('Network error: ' + error.message, 'error');
    } finally {
        setLoading('createUserBtn', false);
    }
}

// Edit user
async function editUser(userId) {
    try {
        const response = await fetch(`${API_BASE_URL}/users/${userId}`, {
            headers: getAuthHeaders()
        });

        const result = await response.json();

        if (response.ok) {
            const user = result.data;
            
            // Populate edit form
            document.getElementById('editUserId').value = user._id;
            document.getElementById('editUsername').value = user.username;
            document.getElementById('editEmail').value = user.email;
            document.getElementById('editRole').value = user.role;
            document.getElementById('editIsActive').checked = user.isActive;
            
            // Show modal
            userModal.style.display = 'block';
        } else {
            showMessage('Failed to load user details', 'error');
        }
    } catch (error) {
        showMessage('Network error: ' + error.message, 'error');
    }
}

// Handle edit user form submission
async function handleEditUser(e) {
    e.preventDefault();
    
    const userId = document.getElementById('editUserId').value;
    const formData = new FormData(editUserForm);
    
    const userData = {
        username: formData.get('username').trim(),
        email: formData.get('email').trim(),
        role: formData.get('role'),
        isActive: document.getElementById('editIsActive').checked
    };

    try {
        const response = await fetch(`${API_BASE_URL}/users/${userId}`, {
            method: 'PUT',
            headers: getAuthHeaders(),
            body: JSON.stringify(userData)
        });

        const result = await response.json();

        if (response.ok) {
            showMessage('User updated successfully!', 'success');
            userModal.style.display = 'none';
            loadUsers();
            loadUserStats();
        } else {
            showMessage(result.error || 'Failed to update user', 'error');
        }
    } catch (error) {
        showMessage('Network error: ' + error.message, 'error');
    }
}

// Toggle user status
async function toggleUserStatus(userId, currentStatus) {
    const action = currentStatus ? 'deactivate' : 'activate';
    
    if (!confirm(`Are you sure you want to ${action} this user?`)) return;

    try {
        const response = await fetch(`${API_BASE_URL}/users/${userId}/toggle-status`, {
            method: 'PUT',
            headers: getAuthHeaders()
        });

        const result = await response.json();

        if (response.ok) {
            showMessage(result.message, 'success');
            loadUsers();
            loadUserStats();
        } else {
            showMessage(result.error || `Failed to ${action} user`, 'error');
        }
    } catch (error) {
        showMessage('Network error: ' + error.message, 'error');
    }
}

// Delete user
async function deleteUser(userId) {
    if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) return;

    try {
        const response = await fetch(`${API_BASE_URL}/users/${userId}`, {
            method: 'DELETE',
            headers: getAuthHeaders()
        });

        const result = await response.json();

        if (response.ok) {
            showMessage('User deleted successfully', 'success');
            loadUsers();
            loadUserStats();
        } else {
            showMessage(result.error || 'Failed to delete user', 'error');
        }
    } catch (error) {
        showMessage('Network error: ' + error.message, 'error');
    }
}

// Utility functions
function setLoading(buttonId, isLoading) {
    const button = document.getElementById(buttonId);
    const btnText = button.querySelector('.btn-text');
    const btnLoading = button.querySelector('.btn-loading');
    
    if (isLoading) {
        button.disabled = true;
        btnText.style.display = 'none';
        btnLoading.style.display = 'inline-flex';
    } else {
        button.disabled = false;
        btnText.style.display = 'inline';
        btnLoading.style.display = 'none';
    }
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function showMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = type === 'error' ? 'error-message' : 'success-message';
    messageDiv.textContent = message;
    messageDiv.style.display = 'block';
    
    const container = document.querySelector('.container');
    container.insertBefore(messageDiv, container.firstChild);
    
    setTimeout(() => {
        messageDiv.remove();
    }, 5000);
}
