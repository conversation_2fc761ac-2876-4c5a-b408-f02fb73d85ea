// Authentication utilities and API calls
class AuthManager {
    constructor() {
        this.baseURL = window.CONFIG ? window.CONFIG.API_BASE_URL : 'http://localhost:5000/api';
        this.token = localStorage.getItem('authToken');
        this.user = JSON.parse(localStorage.getItem('user') || 'null');
    }

    // Store authentication data
    setAuth(token, user) {
        this.token = token;
        this.user = user;
        localStorage.setItem('authToken', token);
        localStorage.setItem('user', JSON.stringify(user));
    }

    // Clear authentication data
    clearAuth() {
        this.token = null;
        this.user = null;
        localStorage.removeItem('authToken');
        localStorage.removeItem('user');
    }

    // Check if user is authenticated
    isAuthenticated() {
        return !!this.token;
    }

    // Get authorization headers
    getAuthHeaders() {
        return {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.token}`
        };
    }

    // Login user
    async login(username, password) {
        try {
            const response = await fetch(`${this.baseURL}/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username, password })
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Login failed');
            }

            this.setAuth(data.data.token, data.data.user);
            return data;
        } catch (error) {
            throw error;
        }
    }

    // Registration method removed - registration is disabled

    // Logout user
    async logout() {
        try {
            if (this.token) {
                await fetch(`${this.baseURL}/auth/logout`, {
                    method: 'POST',
                    headers: this.getAuthHeaders()
                });
            }
        } catch (error) {
            console.error('Logout error:', error);
        } finally {
            this.clearAuth();
        }
    }

    // Verify token
    async verifyToken() {
        if (!this.token) return false;

        try {
            const response = await fetch(`${this.baseURL}/auth/verify-token`, {
                method: 'POST',
                headers: this.getAuthHeaders()
            });

            return response.ok;
        } catch (error) {
            console.error('Token verification error:', error);
            return false;
        }
    }

    // Get current user profile
    async getCurrentUser() {
        try {
            const response = await fetch(`${this.baseURL}/auth/me`, {
                headers: this.getAuthHeaders()
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to get user profile');
            }

            return data.data.user;
        } catch (error) {
            throw error;
        }
    }
}

// Initialize auth manager
const authManager = new AuthManager();

// Utility functions
function showMessage(elementId, message, isError = false) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = message;
        element.style.display = 'block';
        
        // Hide the message after 5 seconds
        setTimeout(() => {
            element.style.display = 'none';
        }, 5000);
    }
}

function setLoading(buttonId, isLoading) {
    const button = document.getElementById(buttonId);
    const btnText = button.querySelector('.btn-text');
    const btnLoading = button.querySelector('.btn-loading');
    
    if (isLoading) {
        button.disabled = true;
        btnText.style.display = 'none';
        btnLoading.style.display = 'inline-flex';
    } else {
        button.disabled = false;
        btnText.style.display = 'inline';
        btnLoading.style.display = 'none';
    }
}

// Login form handler
if (document.getElementById('loginForm')) {
    document.getElementById('loginForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        
        if (!username || !password) {
            showMessage('errorMessage', 'Please fill in all fields', true);
            return;
        }

        setLoading('loginBtn', true);
        
        try {
            await authManager.login(username, password);
            showMessage('successMessage', 'Login successful! Redirecting...');
            
            // Redirect to dashboard after successful login
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1500);
            
        } catch (error) {
            showMessage('errorMessage', error.message, true);
        } finally {
            setLoading('loginBtn', false);
        }
    });
}

// Registration form handler removed - registration is disabled

// Check authentication on page load
document.addEventListener('DOMContentLoaded', async () => {
    const currentPage = window.location.pathname.split('/').pop();
    
    // If on login page and already authenticated, redirect to dashboard
    if (currentPage === 'login.html' && authManager.isAuthenticated()) {
        const isValid = await authManager.verifyToken();
        if (isValid) {
            window.location.href = 'index.html';
            return;
        } else {
            authManager.clearAuth();
        }
    }
    
    // If on dashboard page and not authenticated, redirect to login
    if (currentPage === 'index.html' || currentPage === '') {
        if (!authManager.isAuthenticated()) {
            window.location.href = 'login.html';
            return;
        }
        
        const isValid = await authManager.verifyToken();
        if (!isValid) {
            authManager.clearAuth();
            window.location.href = 'login.html';
            return;
        }
        
        // Dashboard navigation is already in HTML, just update it
        // No need to create duplicate navigation
    }
});


