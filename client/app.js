// API Configuration
const API_BASE_URL = window.CONFIG ? window.CONFIG.API_BASE_URL : 'http://localhost:5000/api';

// Authentication manager (imported from auth.js)
// authManager is already declared in auth.js

// DOM Elements
const profileForm = document.getElementById('profileForm');
const nameInput = document.getElementById('name');
const jobTitleInput = document.getElementById('jobTitle');
const imageInput = document.getElementById('image');
const previewImg = document.getElementById('preview');
const submitBtn = document.getElementById('submitBtn');
const resetBtn = document.getElementById('resetBtn');
const refreshBtn = document.getElementById('refreshBtn');
const loadingIndicator = document.getElementById('loadingIndicator');
const profilesList = document.getElementById('profilesList');
const modal = document.getElementById('profileModal');
const modalContent = document.getElementById('modalContent');

// Initialize authentication manager
document.addEventListener('DOMContentLoaded', () => {
    console.log('App.js DOMContentLoaded, authManager:', authManager);

    // authManager is already initialized in auth.js
    // Just ensure it's available
    if (!authManager) {
        console.error('AuthManager not available');
        window.location.href = 'login.html';
        return;
    }

    console.log('AuthManager available, user:', authManager.user);

    // Update navigation with user data
    updateNavigation();

    loadDashboardStats();
    loadProfiles();
    displayBearerToken();
    setupTokenFunctionality();
});

// Event Listeners
imageInput.addEventListener('change', handleImagePreview);
profileForm.addEventListener('submit', handleFormSubmit);
resetBtn.addEventListener('click', resetForm);
refreshBtn.addEventListener('click', loadProfiles);

// Close modal when clicking the X
document.querySelector('.close').addEventListener('click', () => {
    modal.style.display = 'none';
});

// Close modal when clicking outside
window.addEventListener('click', (e) => {
    if (e.target === modal) {
        modal.style.display = 'none';
    }
});

// Get authentication headers
function getAuthHeaders() {
    const token = localStorage.getItem('authToken');
    return {
        'Authorization': `Bearer ${token}`
    };
}

// Load dashboard statistics
async function loadDashboardStats() {
    try {
        // Get profile statistics
        const profilesResponse = await makeAuthenticatedRequest(`${API_BASE_URL}/user-profiles`);
        if (profilesResponse) {
            const profilesData = await profilesResponse.json();
            if (profilesResponse.ok) {
                const profiles = profilesData.data;

                // Update total profiles
                document.getElementById('totalProfiles').textContent = profiles.length;

                // Calculate today's uploads
                const today = new Date().toDateString();
                const todayUploads = profiles.filter(profile =>
                    new Date(profile.createdAt).toDateString() === today
                ).length;
                document.getElementById('todayUploads').textContent = todayUploads;

                // Calculate storage used (approximate)
                const totalStorage = profiles.reduce((total, profile) => {
                    // Estimate base64 image size (base64 is ~33% larger than original)
                    const imageSize = profile.image ? profile.image.length * 0.75 : 0;
                    return total + imageSize;
                }, 0);

                const storageMB = (totalStorage / (1024 * 1024)).toFixed(2);
                document.getElementById('storageUsed').textContent = `${storageMB} MB`;
            }
        }

        // Get user count if super admin
        if (authManager.user && authManager.user.role === 'super_admin') {
            const usersResponse = await makeAuthenticatedRequest(`${API_BASE_URL}/users/stats`);
            if (usersResponse && usersResponse.ok) {
                const usersData = await usersResponse.json();
                document.getElementById('totalUsers').textContent = usersData.data.total;
            }
        } else {
            document.getElementById('totalUsers').textContent = '1';
        }

    } catch (error) {
        console.error('Error loading dashboard stats:', error);
    }
}

// Make authenticated API request
async function makeAuthenticatedRequest(url, options = {}) {
    const token = localStorage.getItem('authToken');
    
    if (!token) {
        window.location.href = 'login.html';
        return;
    }

    const headers = {
        ...options.headers,
        'Authorization': `Bearer ${token}`
    };

    const response = await fetch(url, {
        ...options,
        headers
    });

    // If unauthorized, redirect to login
    if (response.status === 401) {
        localStorage.removeItem('authToken');
        localStorage.removeItem('user');
        window.location.href = 'login.html';
        return;
    }

    return response;
}

// Image preview handler
function handleImagePreview(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            previewImg.src = e.target.result;
            previewImg.style.display = 'block';
        };
        reader.readAsDataURL(file);
    }
}

// Form submission handler
async function handleFormSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData();
    formData.append('name', nameInput.value.trim());
    formData.append('jobTitle', jobTitleInput.value.trim());
    formData.append('image', imageInput.files[0]);

    try {
        submitBtn.disabled = true;
        submitBtn.textContent = 'Uploading...';

        const response = await makeAuthenticatedRequest(`${API_BASE_URL}/user-profiles`, {
            method: 'POST',
            body: formData
        });

        if (!response) return; // Redirected to login

        const result = await response.json();

        if (response.ok) {
            showMessage('Profile uploaded successfully!', 'success');
            resetForm();
            loadProfiles();
            loadDashboardStats(); // Refresh stats after upload
        } else {
            showMessage(result.error || 'Upload failed', 'error');
        }
    } catch (error) {
        showMessage('Network error: ' + error.message, 'error');
    } finally {
        submitBtn.disabled = false;
        submitBtn.textContent = 'Upload Profile';
    }
}

// Load all profiles
async function loadProfiles() {
    try {
        loadingIndicator.style.display = 'inline';
        refreshBtn.disabled = true;

        const response = await makeAuthenticatedRequest(`${API_BASE_URL}/user-profiles`);
        
        if (!response) return; // Redirected to login

        const result = await response.json();

        if (response.ok) {
            displayProfiles(result.data);
        } else {
            showMessage('Failed to load profiles', 'error');
        }
    } catch (error) {
        showMessage('Network error: ' + error.message, 'error');
    } finally {
        loadingIndicator.style.display = 'none';
        refreshBtn.disabled = false;
    }
}

// Display profiles in grid
function displayProfiles(profiles) {
    if (profiles.length === 0) {
        profilesList.innerHTML = '<p class="loading">No profiles found. Add your first profile!</p>';
        return;
    }

    profilesList.innerHTML = profiles.map(profile => `
        <div class="profile-card">
            <img src="${profile.image}" alt="${profile.name}" class="profile-image">
            <h3 class="profile-name">${profile.name}</h3>
            <p class="profile-job">${profile.jobTitle}</p>
            <p class="profile-date">Added: ${formatDate(profile.createdAt)}</p>
            <div class="profile-actions">
                <button class="btn-small" onclick="viewProfile('${profile._id}')">View</button>
                <button class="btn-small btn-danger" onclick="deleteProfile('${profile._id}')">Delete</button>
            </div>
        </div>
    `).join('');
}

// View profile details
async function viewProfile(id) {
    try {
        const response = await makeAuthenticatedRequest(`${API_BASE_URL}/user-profiles/${id}`);
        
        if (!response) return; // Redirected to login

        const result = await response.json();

        if (response.ok) {
            const profile = result.data;
            modalContent.innerHTML = `
                <img src="${profile.image}" alt="${profile.name}" style="max-width: 100%; height: auto; margin-bottom: 1rem;">
                <h4>${profile.name}</h4>
                <p><strong>Job Title:</strong> ${profile.jobTitle}</p>
                <p><strong>Created:</strong> ${formatDate(profile.createdAt)}</p>
                <p><strong>Profile ID:</strong> ${profile._id}</p>
            `;
            modal.style.display = 'block';
        } else {
            showMessage('Profile not found', 'error');
        }
    } catch (error) {
        showMessage('Network error: ' + error.message, 'error');
    }
}

// Delete profile
async function deleteProfile(id) {
    if (!confirm('Are you sure you want to delete this profile?')) return;

    try {
        const response = await makeAuthenticatedRequest(`${API_BASE_URL}/user-profiles/${id}`, {
            method: 'DELETE'
        });

        if (!response) return; // Redirected to login

        const result = await response.json();

        if (response.ok) {
            showMessage('Profile deleted successfully', 'success');
            loadProfiles();
        } else {
            showMessage(result.error || 'Delete failed', 'error');
        }
    } catch (error) {
        showMessage('Network error: ' + error.message, 'error');
    }
}

// Reset form
function resetForm() {
    profileForm.reset();
    previewImg.style.display = 'none';
}

// Utility functions
function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// Display Bearer token in dashboard
function displayBearerToken() {
    const tokenInput = document.getElementById('bearerToken');
    if (tokenInput && authManager && authManager.token) {
        tokenInput.value = authManager.token;
        updateTokenExamples(authManager.token);
    }
}

// Setup token functionality
function setupTokenFunctionality() {
    // Copy token button
    const copyBtn = document.getElementById('copyTokenBtn');
    if (copyBtn) {
        copyBtn.addEventListener('click', copyTokenToClipboard);
    }

    // Regenerate token button
    const regenerateBtn = document.getElementById('regenerateTokenBtn');
    if (regenerateBtn) {
        regenerateBtn.addEventListener('click', regenerateToken);
    }

    // Show examples button
    const showExamplesBtn = document.getElementById('showExamplesBtn');
    if (showExamplesBtn) {
        showExamplesBtn.addEventListener('click', toggleExamples);
    }
}

// Copy token to clipboard
async function copyTokenToClipboard() {
    const tokenInput = document.getElementById('bearerToken');
    const copyBtn = document.getElementById('copyTokenBtn');
    const btnText = copyBtn.querySelector('.btn-text');
    const btnIcon = copyBtn.querySelector('.btn-icon');

    try {
        await navigator.clipboard.writeText(tokenInput.value);

        // Visual feedback
        const originalText = btnText.textContent;
        const originalIcon = btnIcon.textContent;

        btnIcon.textContent = '✅';
        btnText.textContent = 'Copied!';
        copyBtn.style.background = 'rgba(0, 255, 136, 0.3)';
        copyBtn.style.borderColor = '#00ff88';

        setTimeout(() => {
            btnIcon.textContent = originalIcon;
            btnText.textContent = originalText;
            copyBtn.style.background = '';
            copyBtn.style.borderColor = '';
        }, 2000);

        showMessage('Bearer token copied to clipboard!', 'success');
    } catch (error) {
        // Fallback for older browsers
        tokenInput.select();
        document.execCommand('copy');
        showMessage('Bearer token copied to clipboard!', 'success');
    }
}

// Regenerate token (create new permanent token)
async function regenerateToken() {
    const regenerateBtn = document.getElementById('regenerateTokenBtn');
    const btnText = regenerateBtn.querySelector('.btn-text');
    const btnIcon = regenerateBtn.querySelector('.btn-icon');

    // Confirm action
    if (!confirm('⚠️ This will generate a new permanent token and invalidate the current one.\n\nAny external applications using the old token will need to be updated.\n\nContinue?')) {
        return;
    }

    try {
        const originalText = btnText.textContent;
        const originalIcon = btnIcon.textContent;

        btnIcon.textContent = '⏳';
        btnText.textContent = 'Generating...';
        regenerateBtn.disabled = true;

        const response = await makeAuthenticatedRequest(`${API_BASE_URL}/auth/regenerate-token`, {
            method: 'POST'
        });

        if (response && response.ok) {
            const data = await response.json();

            // Update stored token
            authManager.setAuth(data.data.token, data.data.user);

            // Update display
            displayBearerToken();

            // Success feedback
            btnIcon.textContent = '✅';
            btnText.textContent = 'Generated!';
            regenerateBtn.style.background = 'rgba(0, 255, 136, 0.3)';
            regenerateBtn.style.borderColor = '#00ff88';

            showMessage('New permanent Bearer token generated! Old token is now invalid.', 'success');

            setTimeout(() => {
                btnIcon.textContent = originalIcon;
                btnText.textContent = originalText;
                regenerateBtn.style.background = '';
                regenerateBtn.style.borderColor = '';
            }, 3000);

        } else {
            throw new Error('Failed to regenerate token');
        }
    } catch (error) {
        showMessage('Failed to regenerate token: ' + error.message, 'error');
    } finally {
        regenerateBtn.disabled = false;
    }
}

// Toggle examples section
function toggleExamples() {
    const examplesSection = document.getElementById('examplesSection');
    const showBtn = document.getElementById('showExamplesBtn');
    const btnText = showBtn.querySelector('.btn-text');

    if (examplesSection.style.display === 'none') {
        examplesSection.style.display = 'block';
        btnText.textContent = 'Hide Examples';
        updateTokenExamples(authManager.token);
    } else {
        examplesSection.style.display = 'none';
        btnText.textContent = 'View Examples';
    }
}

// Update token examples with actual token
function updateTokenExamples(token) {
    const shortToken = token.substring(0, 50) + '...';

    // Update curl example
    const curlExample = document.getElementById('curlExample');
    if (curlExample) {
        curlExample.textContent = `curl -H "Authorization: Bearer ${shortToken}" \\
  http://localhost:5000/api/user-profiles`;
    }

    // Update JavaScript example
    const jsExample = document.getElementById('jsExample');
    if (jsExample) {
        jsExample.textContent = `fetch('http://localhost:5000/api/user-profiles', {
  headers: {
    'Authorization': 'Bearer ${shortToken}'
  }
})`;
    }

    // Update Python example
    const pythonExample = document.getElementById('pythonExample');
    if (pythonExample) {
        pythonExample.textContent = `import requests

headers = {'Authorization': 'Bearer ${shortToken}'}
response = requests.get('http://localhost:5000/api/user-profiles', headers=headers)`;
    }
}

// Quick action functions
async function testAPIConnection() {
    try {
        showMessage('Testing API connection...', 'info');

        const response = await makeAuthenticatedRequest(`${API_BASE_URL}/user-profiles`);
        if (response && response.ok) {
            const data = await response.json();
            showMessage(`✅ API connection successful! Found ${data.count} profiles.`, 'success');
        } else {
            throw new Error('API connection failed');
        }
    } catch (error) {
        showMessage('❌ API connection failed: ' + error.message, 'error');
    }
}

function viewAPIEndpoints() {
    const endpoints = `
📋 Available API Endpoints:

🔐 Authentication:
• POST /api/auth/login - Login and get token
• GET /api/auth/me - Get current user info
• POST /api/auth/regenerate-token - Generate new token

👥 User Profiles:
• GET /api/user-profiles - Get all profiles
• POST /api/user-profiles - Create new profile
• PUT /api/user-profiles/:id - Update profile
• DELETE /api/user-profiles/:id - Delete profile

👤 User Management (Super Admin):
• GET /api/users - Get all users
• POST /api/users - Create new user
• PUT /api/users/:id - Update user
• DELETE /api/users/:id - Delete user
    `;

    alert(endpoints);
}

function downloadToken() {
    const token = authManager.token;
    const content = `# Resilient Cape Admin Dashboard - Bearer Token
# Generated: ${new Date().toISOString()}
# Type: Permanent JWT Token
# Usage: Authorization: Bearer <token>

Bearer Token:
${token}

API Base URL:
${API_BASE_URL}

Example Usage:
curl -H "Authorization: Bearer ${token}" \\
  ${API_BASE_URL}/user-profiles
`;

    const blob = new Blob([content], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `bearer-token-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    showMessage('Bearer token downloaded as text file!', 'success');
}

function shareToken() {
    const token = authManager.token;
    const shareText = `Resilient Cape Admin API Access

Bearer Token: ${token}

API Base URL: ${API_BASE_URL}

Usage: Include this header in your requests:
Authorization: Bearer ${token}

Example:
curl -H "Authorization: Bearer ${token}" ${API_BASE_URL}/user-profiles`;

    if (navigator.share) {
        navigator.share({
            title: 'Resilient Cape API Token',
            text: shareText
        });
    } else {
        // Fallback - copy to clipboard
        navigator.clipboard.writeText(shareText).then(() => {
            showMessage('Token details copied to clipboard for sharing!', 'success');
        });
    }
}

function copyExample(type) {
    let code = '';

    switch(type) {
        case 'curl':
            code = document.getElementById('curlExample').textContent;
            break;
        case 'js':
            code = document.getElementById('jsExample').textContent;
            break;
        case 'python':
            code = document.getElementById('pythonExample').textContent;
            break;
    }

    navigator.clipboard.writeText(code).then(() => {
        showMessage(`${type.toUpperCase()} example copied to clipboard!`, 'success');
    });
}

// Update navigation with user data
function updateNavigation() {
    const navUsername = document.getElementById('navUsername');
    const navUserRole = document.getElementById('navUserRole');
    const userManagementLink = document.getElementById('userManagementLink');
    const logoutBtn = document.getElementById('logoutBtn');

    if (authManager && authManager.user) {
        // Update username
        if (navUsername) {
            navUsername.textContent = authManager.user.username;
        }

        // Update role with proper formatting
        if (navUserRole) {
            const roleDisplay = authManager.user.role === 'super_admin'
                ? 'Super Admin'
                : authManager.user.role.charAt(0).toUpperCase() + authManager.user.role.slice(1);

            navUserRole.textContent = roleDisplay;
            navUserRole.className = `user-role ${authManager.user.role}`;
        }

        // Show user management link for super admin
        if (userManagementLink && authManager.user.role === 'super_admin') {
            userManagementLink.style.display = 'inline-block';
        }

        // Add logout functionality
        if (logoutBtn) {
            logoutBtn.addEventListener('click', async () => {
                await authManager.logout();
                window.location.href = 'login.html';
            });
        }
    } else {
        // No user logged in, redirect to login
        window.location.href = 'login.html';
    }
}

function showMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = type;
    messageDiv.textContent = message;

    const container = document.querySelector('.container');
    container.insertBefore(messageDiv, container.firstChild.nextSibling);

    setTimeout(() => {
        messageDiv.remove();
    }, 5000);
}
