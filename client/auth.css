/* Authentication page styles */

/* Import CSS variables for navigation */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --border-radius: 12px;
    --border-radius-lg: 20px;
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
}

.auth-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    padding: 3rem;
    width: 100%;
    max-width: 400px;
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-header h1 {
    color: #333;
    font-size: 2rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.auth-header p {
    color: #666;
    font-size: 1rem;
}

.auth-notice {
    color: #e74c3c !important;
    font-weight: 600 !important;
    font-size: 0.9rem !important;
    margin-top: 0.5rem;
    padding: 0.5rem;
    background: rgba(231, 76, 60, 0.1);
    border-radius: 5px;
    border: 1px solid rgba(231, 76, 60, 0.2);
}

.auth-form {
    margin-bottom: 2rem;
}

.auth-form .form-group {
    margin-bottom: 1.5rem;
}

.auth-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #555;
    font-size: 0.9rem;
}

.auth-form input[type="text"],
.auth-form input[type="password"],
.auth-form input[type="email"],
.auth-form select {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
}

.auth-form input:focus {
    outline: none;
    border-color: #667eea;
    background-color: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.auth-btn {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.auth-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.auth-btn:active {
    transform: translateY(0);
}

.auth-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-loading {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-loading::after {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.error-message {
    background: #fee;
    color: #c33;
    padding: 1rem;
    border-radius: 8px;
    margin-top: 1rem;
    border-left: 4px solid #e74c3c;
    font-size: 0.9rem;
}

.success-message {
    background: #efe;
    color: #3c3;
    padding: 1rem;
    border-radius: 8px;
    margin-top: 1rem;
    border-left: 4px solid #27ae60;
    font-size: 0.9rem;
}

.auth-footer {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid #e1e5e9;
}

.auth-footer p {
    color: #666;
    font-size: 0.9rem;
}

.auth-footer a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.auth-footer a:hover {
    color: #5a6fd8;
    text-decoration: underline;
}

/* Responsive design */
@media (max-width: 480px) {
    .auth-card {
        padding: 2rem 1.5rem;
        margin: 10px;
    }
    
    .auth-header h1 {
        font-size: 1.5rem;
    }
}

/* Modern Dashboard Navigation */
.dashboard-nav {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    padding: 1.5rem 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    border-radius: var(--border-radius-lg);
    border: 1px solid rgba(102, 126, 234, 0.1);
    position: relative;
    overflow: hidden;
    animation: slideInDown 0.6s ease-out;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dashboard-nav::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
}

.nav-brand {
    font-size: 1.5rem;
    font-weight: 700;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-brand::before {
    content: '🛡️';
    font-size: 1.2rem;
    filter: drop-shadow(0 2px 4px rgba(102, 126, 234, 0.3));
}

.nav-links {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 600;
    font-size: 0.95rem;
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--primary-gradient);
    transition: left 0.3s ease;
    z-index: -1;
}

.nav-link:hover::before,
.nav-link.active::before {
    left: 0;
}

.nav-link:hover,
.nav-link.active {
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.nav-user {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
}

.user-name {
    font-weight: 700;
    font-size: 1rem;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.user-name::before {
    content: '👤';
    font-size: 0.9rem;
}

.user-role {
    padding: 0.4rem 1rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 2px solid transparent;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.user-role::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.1;
    transition: opacity 0.3s ease;
}

.user-role:hover::before {
    opacity: 0.2;
}

.user-role.super_admin {
    background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 71, 87, 0.3);
}

.user-role.super_admin::after {
    content: '👑';
    margin-left: 0.5rem;
}

.user-role.admin {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.user-role.admin::after {
    content: '⚡';
    margin-left: 0.5rem;
}

.user-role.moderator {
    background: linear-gradient(135deg, #ffa726 0%, #ff7043 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 167, 38, 0.3);
}

.user-role.moderator::after {
    content: '🔧';
    margin-left: 0.5rem;
}

.user-role.user {
    background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(149, 165, 166, 0.3);
}

.user-role.user::after {
    content: '👥';
    margin-left: 0.5rem;
}

.logout-btn {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 600;
    font-size: 0.9rem;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.logout-btn::before {
    content: '🚪';
    margin-right: 0.5rem;
}

.logout-btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.logout-btn:hover::after {
    left: 100%;
}

.logout-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

/* Navigation Responsive Design */
@media (max-width: 1024px) {
    .dashboard-nav {
        padding: 1.25rem 1.5rem;
    }

    .nav-brand {
        font-size: 1.3rem;
    }

    .nav-links {
        gap: 0.25rem;
    }

    .nav-link {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 768px) {
    .dashboard-nav {
        flex-direction: column;
        gap: 1.5rem;
        padding: 1.5rem;
        text-align: center;
    }

    .nav-brand {
        font-size: 1.4rem;
    }

    .nav-links {
        order: 2;
        justify-content: center;
        flex-wrap: wrap;
    }

    .nav-user {
        order: 3;
        flex-direction: column;
        gap: 1rem;
        width: 100%;
    }

    .user-info {
        align-items: center;
        text-align: center;
    }

    .user-name {
        font-size: 1.1rem;
    }

    .user-role {
        font-size: 0.8rem;
        padding: 0.5rem 1.25rem;
    }

    .logout-btn {
        width: 100%;
        max-width: 200px;
        padding: 1rem;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .dashboard-nav {
        padding: 1rem;
        gap: 1rem;
    }

    .nav-brand {
        font-size: 1.2rem;
    }

    .nav-links {
        flex-direction: column;
        width: 100%;
        gap: 0.5rem;
    }

    .nav-link {
        width: 100%;
        text-align: center;
        padding: 0.75rem;
    }

    .user-info {
        gap: 0.75rem;
    }
}

.nav-brand {
    font-size: 1.5rem;
    font-weight: 600;
    color: #667eea;
}

.nav-user {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.user-name {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

.user-role {
    color: #666;
    font-size: 0.8rem;
    text-transform: capitalize;
}

.logout-btn {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.3s;
}

.logout-btn:hover {
    background: #c0392b;
}
