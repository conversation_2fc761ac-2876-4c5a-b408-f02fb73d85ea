// Configuration for different environments
const config = {
  // Determine API base URL based on environment
  API_BASE_URL: (() => {
    const hostname = window.location.hostname;
    const protocol = window.location.protocol;
    
    // Local development
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
      return 'http://localhost:5000/api';
    }
    
    // Vercel deployment or custom domain
    return `${protocol}//${hostname}/api`;
  })(),
  
  // Other configuration options
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_FILE_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  TOKEN_STORAGE_KEY: 'authToken',
  USER_STORAGE_KEY: 'user'
};

// Make config globally available
window.CONFIG = config;

// Log current configuration (for debugging)
console.log('App Configuration:', {
  API_BASE_URL: config.API_BASE_URL,
  Environment: window.location.hostname === 'localhost' ? 'Development' : 'Production'
});
