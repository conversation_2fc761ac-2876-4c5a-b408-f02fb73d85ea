<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Resilient Cape Admin Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="auth.css">
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <h1>Admin Login</h1>
                <p>Sign in to access the dashboard</p>
                <p class="auth-notice">Only existing accounts can access this system</p>
            </div>

            <form id="loginForm" class="auth-form">
                <div class="form-group">
                    <label for="username">Username or Email</label>
                    <input type="text" id="username" name="username" required autocomplete="username">
                </div>

                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required autocomplete="current-password">
                </div>

                <div class="form-group">
                    <button type="submit" id="loginBtn" class="auth-btn">
                        <span class="btn-text">Sign In</span>
                        <span class="btn-loading" style="display: none;">Signing in...</span>
                    </button>
                </div>

                <div id="errorMessage" class="error-message" style="display: none;"></div>
                <div id="successMessage" class="success-message" style="display: none;"></div>
            </form>

            <!-- Registration removed - only existing accounts can access -->
        </div>
    </div>

    <script src="config.js"></script>
    <script src="auth.js"></script>
</body>
</html>
