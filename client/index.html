<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resilient Cape Admin Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="auth.css">
</head>
<body>
    <div class="container">
        <!-- Navigation Bar -->
        <nav class="dashboard-nav">
            <div class="nav-brand">Resilient Cape Admin</div>
            <div class="nav-links">
                <a href="index.html" class="nav-link active">Dashboard</a>
                <a href="users.html" class="nav-link" id="userManagementLink" style="display: none;">User Management</a>
            </div>
            <div class="nav-user">
                <div class="user-info">
                    <span class="user-name" id="navUsername">Loading...</span>
                    <span class="user-role" id="navUserRole">user</span>
                </div>
                <button id="logoutBtn" class="logout-btn">Logout</button>
            </div>
        </nav>

        <header>
            <h1>Admin Dashboard - User Profile Management</h1>
        </header>

        <main>
            <!-- Dashboard Statistics Section -->
            <section class="dashboard-stats">
                <h2>Dashboard Overview</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3 id="totalProfiles">0</h3>
                        <p>Total Profiles</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="todayUploads">0</h3>
                        <p>Today's Uploads</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="totalUsers">0</h3>
                        <p>System Users</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="storageUsed">0 MB</h3>
                        <p>Storage Used</p>
                    </div>
                </div>
            </section>

            <!-- Bearer Token Section -->
            <section class="token-section">
                <div class="section-header">
                    <h2>🔑 Permanent API Access Token</h2>
                    <p>Your permanent Bearer token for external applications. Never expires unless regenerated.</p>
                </div>

                <div class="token-main-container">
                    <div class="token-display-card">
                        <div class="token-header">
                            <h3>Your Bearer Token</h3>
                            <div class="token-status">
                                <span class="status-indicator active"></span>
                                <span>Active & Permanent</span>
                            </div>
                        </div>

                        <div class="token-field">
                            <input type="text" id="bearerToken" readonly placeholder="Loading your permanent token...">
                        </div>

                        <div class="token-actions">
                            <button id="copyTokenBtn" class="action-btn copy-btn">
                                <span class="btn-icon">📋</span>
                                <span class="btn-text">Copy Token</span>
                            </button>
                            <button id="regenerateTokenBtn" class="action-btn regenerate-btn">
                                <span class="btn-icon">🔄</span>
                                <span class="btn-text">Generate New Token</span>
                            </button>
                            <button id="showExamplesBtn" class="action-btn examples-btn">
                                <span class="btn-icon">📖</span>
                                <span class="btn-text">View Examples</span>
                            </button>
                        </div>

                        <div class="token-info-grid">
                            <div class="info-item">
                                <span class="info-label">Type:</span>
                                <span class="info-value">JWT Bearer Token</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Expiration:</span>
                                <span class="info-value">Never (Permanent)</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Usage:</span>
                                <code class="usage-code">Authorization: Bearer &lt;token&gt;</code>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Security:</span>
                                <span class="info-value">Regenerate if compromised</span>
                            </div>
                        </div>
                    </div>

                    <div class="api-quick-actions">
                        <h3>Quick API Actions</h3>
                        <div class="quick-actions-grid">
                            <button class="quick-action-btn" onclick="testAPIConnection()">
                                <span class="action-icon">🔍</span>
                                <span class="action-title">Test API</span>
                                <span class="action-desc">Test connection</span>
                            </button>
                            <button class="quick-action-btn" onclick="viewAPIEndpoints()">
                                <span class="action-icon">📋</span>
                                <span class="action-title">View Endpoints</span>
                                <span class="action-desc">See all available APIs</span>
                            </button>
                            <button class="quick-action-btn" onclick="downloadToken()">
                                <span class="action-icon">💾</span>
                                <span class="action-title">Download Token</span>
                                <span class="action-desc">Save as text file</span>
                            </button>
                            <button class="quick-action-btn" onclick="shareToken()">
                                <span class="action-icon">📤</span>
                                <span class="action-title">Share Token</span>
                                <span class="action-desc">Generate share link</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Collapsible Examples Section -->
                <div id="examplesSection" class="examples-section" style="display: none;">
                    <h3>API Usage Examples</h3>
                    <div class="examples-grid">
                        <div class="example-card">
                            <h4>curl Command</h4>
                            <pre><code id="curlExample">curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:5000/api/user-profiles</code></pre>
                            <button onclick="copyExample('curl')" class="copy-example-btn">Copy</button>
                        </div>
                        <div class="example-card">
                            <h4>JavaScript</h4>
                            <pre><code id="jsExample">fetch('http://localhost:5000/api/user-profiles', {
  headers: {
    'Authorization': 'Bearer YOUR_TOKEN'
  }
})</code></pre>
                            <button onclick="copyExample('js')" class="copy-example-btn">Copy</button>
                        </div>
                        <div class="example-card">
                            <h4>Python</h4>
                            <pre><code id="pythonExample">import requests

headers = {'Authorization': 'Bearer YOUR_TOKEN'}
response = requests.get('http://localhost:5000/api/user-profiles', headers=headers)</code></pre>
                            <button onclick="copyExample('python')" class="copy-example-btn">Copy</button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Upload Form Section -->
            <section class="upload-section">
                <h2>Add New User Profile</h2>
                <form id="profileForm" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="name">Full Name *</label>
                        <input type="text" id="name" name="name" required maxlength="100">
                    </div>

                    <div class="form-group">
                        <label for="jobTitle">Job Title *</label>
                        <input type="text" id="jobTitle" name="jobTitle" required maxlength="100">
                    </div>

                    <div class="form-group">
                        <label for="image">Profile Image *</label>
                        <input type="file" id="image" name="image" accept=".jpg,.jpeg,.png,.webp" required>
                        <small>Accepted formats: JPG, PNG, WebP (Max 5MB)</small>
                    </div>

                    <div class="form-group">
                        <img id="preview" src="#" alt="Image Preview" style="display: none; max-width: 200px; max-height: 200px;">
                    </div>

                    <button type="submit" id="submitBtn">Upload Profile</button>
                    <button type="button" id="resetBtn" style="margin-left: 10px;">Reset Form</button>
                </form>
            </section>

            <!-- Profiles List Section -->
            <section class="profiles-section">
                <h2>User Profiles</h2>
                <div class="controls">
                    <button id="refreshBtn">Refresh List</button>
                    <span id="loadingIndicator" style="display: none;">Loading...</span>
                </div>
                <div id="profilesList" class="profiles-grid">
                    <!-- Profiles will be dynamically loaded here -->
                </div>
            </section>
        </main>

        <!-- Modal for viewing profile details -->
        <div id="profileModal" class="modal">
            <div class="modal-content">
                <span class="close">&times;</span>
                <h3>Profile Details</h3>
                <div id="modalContent"></div>
            </div>
        </div>
    </div>

    <script src="config.js"></script>
    <script src="auth.js"></script>
    <script src="app.js"></script>
</body>
</html>
