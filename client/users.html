<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - Resilient Cape Admin Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="auth.css">
</head>
<body>
    <div class="container">
        <main>
            <!-- User Statistics Section -->
            <section class="stats-section">
                <h2>User Statistics</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3 id="totalUsers">0</h3>
                        <p>Total Users</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="activeUsers">0</h3>
                        <p>Active Users</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="inactiveUsers">0</h3>
                        <p>Inactive Users</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="totalProfiles">0</h3>
                        <p>User Profiles</p>
                    </div>
                </div>
            </section>

            <!-- Create User Section -->
            <section class="create-user-section">
                <h2>Create New User</h2>
                <p class="section-description">Only Super Admin can create new users. Public registration is disabled.</p>
                <form id="createUserForm" class="user-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="newUsername">Username *</label>
                            <input type="text" id="newUsername" name="username" required minlength="3" maxlength="50">
                        </div>
                        <div class="form-group">
                            <label for="newEmail">Email *</label>
                            <input type="email" id="newEmail" name="email" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="newPassword">Password *</label>
                            <input type="password" id="newPassword" name="password" required minlength="6">
                        </div>
                        <div class="form-group">
                            <label for="newRole">Role *</label>
                            <select id="newRole" name="role" required>
                                <option value="user">User</option>
                                <option value="moderator">Moderator</option>
                                <option value="admin">Admin</option>
                            </select>
                        </div>
                    </div>
                    <button type="submit" id="createUserBtn" class="auth-btn">
                        <span class="btn-text">Create User</span>
                        <span class="btn-loading" style="display: none;">Creating...</span>
                    </button>
                </form>
            </section>

            <!-- Users List Section -->
            <section class="users-section">
                <h2>Manage Users</h2>
                <div class="controls">
                    <button id="refreshUsersBtn">Refresh List</button>
                    <span id="usersLoadingIndicator" style="display: none;">Loading...</span>
                </div>
                <div id="usersList" class="users-grid">
                    <!-- Users will be dynamically loaded here -->
                </div>
            </section>
        </main>

        <!-- User Edit Modal -->
        <div id="userModal" class="modal">
            <div class="modal-content">
                <span class="close" id="closeUserModal">&times;</span>
                <h3>Edit User</h3>
                <form id="editUserForm">
                    <input type="hidden" id="editUserId">
                    <div class="form-group">
                        <label for="editUsername">Username</label>
                        <input type="text" id="editUsername" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="editEmail">Email</label>
                        <input type="email" id="editEmail" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="editRole">Role</label>
                        <select id="editRole" name="role" required>
                            <option value="user">User</option>
                            <option value="moderator">Moderator</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="editIsActive" name="isActive">
                            Account Active
                        </label>
                    </div>
                    <div class="modal-actions">
                        <button type="submit" class="auth-btn">Update User</button>
                        <button type="button" id="cancelEdit" class="btn-secondary">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="config.js"></script>
    <script src="auth.js"></script>
    <script src="users.js"></script>
</body>
</html>
